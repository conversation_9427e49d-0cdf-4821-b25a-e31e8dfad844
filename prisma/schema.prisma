// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User model
model User {
  id          String   @id @default(cuid())
  telegramId  BigInt   @unique
  username    String?
  firstName   String?
  lastName    String?
  isActive    Boolean  @default(true)
  isPremium   Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  subscriptions UserSubscription[]
  notifications NotificationMessage[]

  @@map("users")
}

// Cryptocurrency model
model Cryptocurrency {
  id              String     @id @default(cuid())
  symbol          String     @unique
  name            String
  coinGeckoId     String     @unique
  contractAddress String?
  blockchain      Blockchain
  isActive        Boolean    @default(true)
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @updatedAt

  // Relations
  subscriptions     UserSubscription[]
  priceData         PriceData[]
  historicalPrices  HistoricalPrice[]
  technicalSignals  TechnicalSignal[]
  chainAlerts       ChainAlert[]

  @@map("cryptocurrencies")
}

// User subscription model
model UserSubscription {
  id                   String              @id @default(cuid())
  userId               String
  coinId               String
  isActive             Boolean             @default(true)
  enablePriceAlerts    Boolean             @default(true)
  enableTechnicalSignals Boolean           @default(true)
  enableChainAlerts    Boolean             @default(true)
  pushFrequency        PushFrequency       @default(DAILY)
  priceChangeThreshold Float               @default(5.0)
  volumeChangeThreshold Float              @default(20.0)
  createdAt            DateTime            @default(now())
  updatedAt            DateTime            @updatedAt

  // Relations
  user User           @relation(fields: [userId], references: [id], onDelete: Cascade)
  coin Cryptocurrency @relation(fields: [coinId], references: [id], onDelete: Cascade)

  @@unique([userId, coinId])
  @@map("user_subscriptions")
}

// Price data model
model PriceData {
  id                        String   @id @default(cuid())
  coinId                    String
  price                     Float
  priceChange24h            Float
  priceChangePercentage24h  Float
  marketCap                 Float
  volume24h                 Float
  timestamp                 DateTime @default(now())

  // Relations
  coin Cryptocurrency @relation(fields: [coinId], references: [id], onDelete: Cascade)

  @@unique([coinId, timestamp])
  @@map("price_data")
}

// Historical price model
model HistoricalPrice {
  id        String   @id @default(cuid())
  coinId    String
  price     Float
  volume    Float
  marketCap Float
  timestamp DateTime

  // Relations
  coin Cryptocurrency @relation(fields: [coinId], references: [id], onDelete: Cascade)

  @@unique([coinId, timestamp])
  @@map("historical_prices")
}

// Technical signal model
model TechnicalSignal {
  id          String        @id @default(cuid())
  coinId      String
  signalType  SignalType
  strength    SignalStrength
  description String
  macdValue   Float
  macdSignal  Float
  macdHistogram Float
  rsiValue    Float
  isPulseSignal Boolean     @default(false)
  createdAt   DateTime      @default(now())

  // Relations
  coin Cryptocurrency @relation(fields: [coinId], references: [id], onDelete: Cascade)

  @@map("technical_signals")
}

// Chain transaction model
model ChainTransaction {
  id                String     @id @default(cuid())
  hash              String     @unique
  fromAddress       String
  toAddress         String
  value             String     // Store as string to handle large numbers
  valueUSD          Float
  blockchain        Blockchain
  blockNumber       BigInt
  timestamp         DateTime
  isLargeTransaction Boolean   @default(false)
  createdAt         DateTime   @default(now())

  @@map("chain_transactions")
}

// Whale address model
model WhaleAddress {
  id           String     @id @default(cuid())
  address      String
  blockchain   Blockchain
  balance      String     // Store as string to handle large numbers
  balanceUSD   Float
  label        String?
  isExchange   Boolean    @default(false)
  lastActivity DateTime
  createdAt    DateTime   @default(now())
  updatedAt    DateTime   @updatedAt

  @@unique([address, blockchain])
  @@map("whale_addresses")
}

// Chain alert model
model ChainAlert {
  id          String          @id @default(cuid())
  coinId      String?
  alertType   ChainAlertType
  blockchain  Blockchain
  description String
  data        Json            // Store additional data as JSON
  severity    AlertSeverity
  createdAt   DateTime        @default(now())

  // Relations
  coin Cryptocurrency? @relation(fields: [coinId], references: [id], onDelete: SetNull)

  @@map("chain_alerts")
}

// Notification message model
model NotificationMessage {
  id          String              @id @default(cuid())
  userId      String
  type        NotificationType
  title       String
  message     String
  data        Json?               // Store additional data as JSON
  priority    NotificationPriority
  isRead      Boolean             @default(false)
  isSent      Boolean             @default(false)
  scheduledAt DateTime?
  sentAt      DateTime?
  createdAt   DateTime            @default(now())

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("notification_messages")
}

// Enums
enum Blockchain {
  ETHEREUM
  BSC
  POLYGON
  ARBITRUM
  OPTIMISM
}

enum PushFrequency {
  HOURLY
  EVERY_4_HOURS
  DAILY
  WEEKLY
}

enum SignalType {
  BUY
  SELL
  HOLD
  STRONG_BUY
  STRONG_SELL
}

enum SignalStrength {
  WEAK
  MODERATE
  STRONG
  VERY_STRONG
}

enum ChainAlertType {
  LARGE_TRANSACTION
  WHALE_MOVEMENT
  EXCHANGE_INFLOW
  EXCHANGE_OUTFLOW
  UNUSUAL_VOLUME
}

enum AlertSeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum NotificationType {
  PRICE_ALERT
  TECHNICAL_SIGNAL
  CHAIN_ALERT
  SYSTEM_NOTIFICATION
}

enum NotificationPriority {
  LOW
  NORMAL
  HIGH
  URGENT
}
