version: '3.8'

services:
  # Development application with hot reload
  app:
    build:
      context: .
      target: development
    container_name: crypto-bot-dev
    restart: unless-stopped
    ports:
      - "3000:3000"
      - "9229:9229" # Debug port
    environment:
      - NODE_ENV=development
      - DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD:-password}@postgres:5432/crypto_bot_dev
      - REDIS_URL=redis://redis:6379
    env_file:
      - .env.example
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - .:/app
      - /app/node_modules
      - ./logs:/app/logs
    networks:
      - crypto-bot-dev-network
    command: npm run dev

  # PostgreSQL database for development
  postgres:
    image: postgres:15-alpine
    container_name: crypto-bot-postgres-dev
    restart: unless-stopped
    environment:
      POSTGRES_DB: crypto_bot_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-password}
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    ports:
      - "5433:5432" # Different port to avoid conflicts
    networks:
      - crypto-bot-dev-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d crypto_bot_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for development
  redis:
    image: redis:7-alpine
    container_name: crypto-bot-redis-dev
    restart: unless-stopped
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6380:6379" # Different port to avoid conflicts
    networks:
      - crypto-bot-dev-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # Database administration tool
  adminer:
    image: adminer:latest
    container_name: crypto-bot-adminer
    restart: unless-stopped
    ports:
      - "8080:8080"
    depends_on:
      - postgres
    networks:
      - crypto-bot-dev-network

  # Redis administration tool
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: crypto-bot-redis-commander
    restart: unless-stopped
    environment:
      - REDIS_HOSTS=local:redis:6379
    ports:
      - "8081:8081"
    depends_on:
      - redis
    networks:
      - crypto-bot-dev-network

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  crypto-bot-dev-network:
    driver: bridge
