# 加密货币交易辅助Telegram机器人

一个专业的加密货币交易辅助Telegram机器人，提供实时技术指标分析、链上数据监控和智能推送服务。

## 🚀 主要功能

### 📊 技术指标监控
- **MACD脉冲信号检测**: 自动识别MACD线穿越信号线且柱状图由负转正的强烈买入信号
- **RSI指标分析**: 监控RSI超买超卖状态，结合MACD提供更准确的交易信号
- **多指标组合**: 同时满足MACD和RSI条件的币种优先推送

### ⛓️ 链上数据分析
- **大额转账监控**: 实时监控以太坊和BSC链上的大额转账活动
- **巨鲸地址追踪**: 跟踪知名巨鲸地址的资金流动
- **交易所资金流动**: 监控主要交易所的资金流入流出情况
- **异常活动提醒**: 检测异常交易量和可疑活动

### ⏰ 智能推送系统
- **个性化推送频率**: 支持每小时、每4小时、每日、每周推送
- **自定义关注列表**: 用户可订阅感兴趣的加密货币
- **智能过滤**: 根据用户设置的阈值过滤推送内容
- **多级权限管理**: 免费用户和高级用户不同的功能权限

### 💎 用户管理
- **订阅管理**: 灵活的币种订阅和取消订阅
- **个人设置**: 自定义推送频率、价格阈值等参数
- **权限分级**: 免费用户限制5个订阅，高级用户无限制
- **使用统计**: 详细的使用数据和分析报告

## 🛠️ 技术架构

### 后端技术栈
- **Node.js + TypeScript**: 类型安全的服务端开发
- **Telegraf**: 强大的Telegram Bot框架
- **Prisma ORM**: 现代化的数据库访问层
- **PostgreSQL**: 可靠的关系型数据库
- **Redis**: 高性能缓存和会话存储

### 外部API集成
- **CoinGecko API**: 获取实时价格和市场数据
- **Etherscan API**: 以太坊链上数据监控
- **BSCScan API**: BSC链上数据监控
- **Technical Indicators**: 专业的技术指标计算库

### 部署和监控
- **Docker**: 容器化部署
- **Docker Compose**: 多服务编排
- **Winston**: 结构化日志记录
- **Prometheus + Grafana**: 系统监控和可视化

## 📋 快速开始

### 环境要求
- Node.js 18+
- Docker & Docker Compose
- PostgreSQL 15+
- Redis 7+

### 1. 克隆项目
```bash
git clone https://github.com/Rabbit937/tg-bot-demo.git
cd tg-bot-demo
```

### 2. 环境配置
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑配置文件，填入必要的API密钥
nano .env
```

### 3. 启动开发环境
```bash
# 使用Docker Compose启动所有服务
docker-compose -f docker-compose.dev.yml up -d

# 或者本地开发
npm install
npm run dev
```

### 4. 数据库初始化
```bash
# 运行数据库迁移
npm run db:migrate

# 初始化加密货币数据
npm run init-data
```

## 🔧 配置说明

### 必需的API密钥
1. **Telegram Bot Token**: 从 [@BotFather](https://t.me/botfather) 获取
2. **Etherscan API Key**: 从 [Etherscan](https://etherscan.io/apis) 获取
3. **BSCScan API Key**: 从 [BSCScan](https://bscscan.com/apis) 获取
4. **CoinGecko API Key**: 可选，从 [CoinGecko](https://www.coingecko.com/en/api) 获取

### 环境变量配置
```bash
# Telegram Bot
TELEGRAM_BOT_TOKEN=your_bot_token

# 数据库
DATABASE_URL=postgresql://user:password@localhost:5432/crypto_bot_db

# API密钥
ETHERSCAN_API_KEY=your_etherscan_key
BSCSCAN_API_KEY=your_bscscan_key
COINGECKO_API_KEY=your_coingecko_key

# 其他配置
NODE_ENV=development
LOG_LEVEL=info
```

## 📱 机器人命令

### 基础命令
- `/start` - 开始使用机器人
- `/help` - 查看帮助信息
- `/status` - 查看机器人状态

### 订阅管理
- `/subscribe` - 订阅币种监控
- `/unsubscribe` - 取消订阅
- `/list` - 查看订阅列表
- `/settings` - 个人设置

### 市场数据
- `/price <币种>` - 查询实时价格
- 例如：`/price BTC`

## 🏗️ 项目结构

```
src/
├── bot/                 # Telegram Bot相关
│   ├── commands/        # 命令处理器
│   ├── middleware/      # 中间件
│   └── index.ts         # Bot主文件
├── services/            # 业务服务
│   ├── CoinGeckoService.ts
│   ├── TechnicalAnalysisService.ts
│   ├── ChainMonitoringService.ts
│   └── NotificationService.ts
├── models/              # 数据模型
├── utils/               # 工具函数
├── config/              # 配置管理
├── types/               # TypeScript类型定义
└── schedulers/          # 定时任务
```

## 🔍 技术指标说明

### MACD脉冲信号
- **条件**: MACD线穿越信号线向上，且柱状图由负转正
- **意义**: 强烈的买入信号，通常预示价格上涨
- **配合**: 结合RSI指标确保不在超买区域

### RSI指标
- **超卖**: RSI < 30，可能的买入机会
- **超买**: RSI > 70，可能的卖出机会
- **正常区间**: 30-70，适合配合其他指标使用

## 📊 监控和告警

### 系统监控
- **健康检查**: 自动检测服务状态
- **性能监控**: CPU、内存、网络使用情况
- **API监控**: 外部API响应时间和成功率

### 告警机制
- **服务异常**: 自动发送告警通知
- **API限制**: 监控API使用量和限制
- **数据异常**: 检测异常的市场数据

## 🚀 部署指南

详细的部署说明请参考 [DEPLOYMENT.md](./DEPLOYMENT.md)

### 快速部署
```bash
# 生产环境部署
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f app
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## ⚠️ 免责声明

本机器人提供的信息仅供参考，不构成投资建议。加密货币投资存在高风险，请谨慎决策并自行承担投资风险。

## 📞 支持

- **GitHub Issues**: [提交问题](https://github.com/Rabbit937/tg-bot-demo/issues)
- **文档**: [查看文档](./docs/)
- **示例**: [查看示例](./examples/)

## 🎯 路线图

- [ ] 支持更多技术指标（布林带、KDJ等）
- [ ] 添加更多区块链支持（Polygon、Arbitrum等）
- [ ] 实现图表生成功能
- [ ] 添加社交情绪分析
- [ ] 支持自定义策略配置
- [ ] 移动端应用开发

---

**⭐ 如果这个项目对您有帮助，请给我们一个星标！**
