import { Telegraf } from 'telegraf';
import { BotContextWithSession } from './index';
import { UserService } from '../services/UserService';
import { BotContext } from '../types';
import logger from '../config/logger';

export function setupMiddleware(
  bot: Telegraf<BotContextWithSession>,
  userService: UserService
): void {
  // User authentication and context middleware
  bot.use(async (ctx, next) => {
    try {
      if (!ctx.from) {
        return;
      }

      const telegramId = ctx.from.id;
      const chatId = ctx.chat?.id;
      const messageId = ctx.message?.message_id;

      // Get or create user
      let user = await userService.getUserByTelegramId(telegramId);
      
      if (!user) {
        user = await userService.createUser({
          telegramId,
          username: ctx.from.username,
          firstName: ctx.from.first_name,
          lastName: ctx.from.last_name,
        });
        
        logger.info(`New user registered: ${user.id} (${telegramId})`);
      } else {
        // Update user info if changed
        const updateData: Partial<typeof user> = {};
        
        if (user.username !== ctx.from.username) {
          updateData.username = ctx.from.username;
        }
        if (user.firstName !== ctx.from.first_name) {
          updateData.firstName = ctx.from.first_name;
        }
        if (user.lastName !== ctx.from.last_name) {
          updateData.lastName = ctx.from.last_name;
        }
        
        if (Object.keys(updateData).length > 0) {
          user = await userService.updateUser(user.id, updateData);
        }
      }

      // Set session user
      ctx.session.user = user;

      // Create bot context
      ctx.botContext = {
        userId: user.id,
        chatId: chatId || 0,
        messageId: messageId || 0,
        user,
      };

      await next();
    } catch (error) {
      logger.error('Middleware error:', error);
      ctx.reply('认证失败，请稍后再试。');
    }
  });

  // Logging middleware
  bot.use(async (ctx, next) => {
    const start = Date.now();
    const userId = ctx.session.user?.id || 'unknown';
    const command = ctx.message && 'text' in ctx.message ? ctx.message.text : ctx.updateType;
    
    logger.info(`User ${userId} executed: ${command}`);
    
    await next();
    
    const duration = Date.now() - start;
    logger.info(`Command processed in ${duration}ms`);
  });

  // Rate limiting middleware (basic implementation)
  const userLastAction = new Map<number, number>();
  const RATE_LIMIT_MS = 1000; // 1 second between actions

  bot.use(async (ctx, next) => {
    if (!ctx.from) {
      return;
    }

    const userId = ctx.from.id;
    const now = Date.now();
    const lastAction = userLastAction.get(userId) || 0;

    if (now - lastAction < RATE_LIMIT_MS) {
      logger.warn(`Rate limit exceeded for user ${userId}`);
      return ctx.reply('请稍慢一点，不要发送太频繁。');
    }

    userLastAction.set(userId, now);
    await next();
  });

  // Error boundary middleware
  bot.use(async (ctx, next) => {
    try {
      await next();
    } catch (error) {
      logger.error('Command execution error:', error);
      
      if (error instanceof Error) {
        if (error.message.includes('Forbidden')) {
          logger.warn(`Bot was blocked by user ${ctx.from?.id}`);
          return;
        }
      }
      
      ctx.reply('执行命令时发生错误，请稍后再试。');
    }
  });
}
