import { BotContextWithSession } from '../index';

export async function statusCommand(ctx: BotContextWithSession): Promise<void> {
  const user = ctx.session.user;
  const uptime = process.uptime();
  const uptimeHours = Math.floor(uptime / 3600);
  const uptimeMinutes = Math.floor((uptime % 3600) / 60);

  const message = `
🤖 **机器人状态**

**系统信息：**
• 状态：🟢 正常运行
• 运行时间：${uptimeHours}小时 ${uptimeMinutes}分钟
• 版本：v1.0.0

**用户信息：**
• 用户ID：${user?.id}
• Telegram ID：${user?.telegramId}
• 注册时间：${user?.createdAt ? new Date(user.createdAt).toLocaleDateString('zh-CN') : '未知'}
• 会员状态：${user?.isPremium ? '💎 高级会员' : '👤 普通用户'}

**服务状态：**
• 🟢 价格数据服务：正常
• 🟢 技术指标分析：正常
• 🟢 链上数据监控：正常
• 🟢 推送服务：正常

**今日统计：**
• 处理信号：0个
• 发送通知：0条
• API调用：0次

如有问题，请联系管理员。
`;

  await ctx.reply(message);
}
