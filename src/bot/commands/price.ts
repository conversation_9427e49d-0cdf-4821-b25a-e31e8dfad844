import { BotContextWithSession } from '../index';

export async function priceCommand(ctx: BotContextWithSession): Promise<void> {
  const text = ctx.message && 'text' in ctx.message ? ctx.message.text : '';
  const args = text.split(' ').slice(1); // Remove /price command
  
  if (args.length === 0) {
    const message = `
💰 **价格查询**

请输入币种符号，例如：
/price BTC
/price ETH
/price ADA

**支持的格式：**
• /price BTC - 查询比特币价格
• /price ETH USDT - 查询以太坊对USDT价格
• /price BTC,ETH,ADA - 查询多个币种

**即将支持：**
• 实时价格数据
• 24小时涨跌幅
• 市值和交易量
• 技术指标概览
• 价格图表
`;

    await ctx.reply(message);
    return;
  }

  const symbol = args[0].toUpperCase();
  
  // This will be implemented when price service is ready
  const message = `
💰 **${symbol} 价格信息**

此功能正在开发中...

即将显示：
• 💵 当前价格
• 📈 24小时涨跌幅
• 📊 市值排名
• 💧 24小时交易量
• 📉 技术指标概览

请稍后再试。
`;

  await ctx.reply(message);
}
