import { BotContextWithSession } from '../index';

export async function helpCommand(ctx: BotContextWithSession): Promise<void> {
  const helpMessage = `
📖 **命令帮助**

**基础命令：**
/start - 开始使用机器人
/help - 显示此帮助信息
/status - 查看机器人状态

**订阅管理：**
/subscribe - 订阅币种监控
/unsubscribe - 取消订阅
/list - 查看我的订阅列表
/settings - 个人设置

**市场数据：**
/price <币种> - 查询实时价格
例如：/price BTC

**功能说明：**

🔍 **技术指标监控**
自动监控MACD和RSI指标，当出现买入/卖出信号时及时通知您。

⛓️ **链上数据分析**
监控大额转账、巨鲸活动和交易所资金流动，帮助您把握市场动向。

⚙️ **个性化设置**
可自定义推送频率、价格阈值和关注的币种列表。

💡 **使用技巧：**
• 建议订阅3-5个您最关注的币种
• 设置合理的价格变化阈值（建议5-10%）
• 定期查看技术指标信号

如有问题，请联系管理员。
`;

  await ctx.reply(helpMessage);
}
