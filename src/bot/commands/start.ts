import { BotContextWithSession } from '../index';

export async function startCommand(ctx: BotContextWithSession): Promise<void> {
  const user = ctx.session.user;
  const firstName = user?.firstName || '朋友';

  const welcomeMessage = `
🚀 欢迎使用加密货币交易辅助机器人！

你好 ${firstName}！我是您的专业加密货币分析助手。

🔥 **主要功能：**

📊 **技术指标监控**
• MACD脉冲信号检测
• RSI指标分析
• 多指标组合信号

⛓️ **链上数据分析**
• 大额转账监控
• 巨鲸地址追踪
• 交易所资金流动

⏰ **智能推送系统**
• 自定义推送频率
• 个性化关注列表
• 实时价格提醒

💎 **使用指南：**
/help - 查看所有命令
/subscribe - 订阅币种监控
/list - 查看我的订阅
/price - 查询实时价格
/settings - 个人设置

让我们开始您的加密货币投资之旅吧！🌟
`;

  await ctx.reply(welcomeMessage);
}
