import { Markup } from 'telegraf';
import { BotContextWithSession } from '../index';

export async function subscribeCommand(ctx: BotContextWithSession): Promise<void> {
  const popularCoins = [
    { symbol: 'BTC', name: 'Bitcoin' },
    { symbol: 'ETH', name: 'Ether<PERSON>' },
    { symbol: 'BNB', name: 'BNB' },
    { symbol: 'ADA', name: 'Cardano' },
    { symbol: 'SOL', name: '<PERSON><PERSON>' },
    { symbol: 'DOT', name: 'Polk<PERSON>t' },
    { symbol: 'MATIC', name: 'Polygon' },
    { symbol: 'AVAX', name: 'Avalanche' },
  ];

  const keyboard = Markup.inlineKeyboard([
    ...popularCoins.map(coin => [
      Markup.button.callback(
        `${coin.symbol} (${coin.name})`,
        `subscribe_${coin.symbol.toLowerCase()}`
      )
    ]),
    [Markup.button.callback('🔍 自定义币种', 'subscribe_custom')]
  ]);

  const message = `
📈 **订阅币种监控**

请选择您要监控的加密货币：

**热门币种：**
选择下方按钮快速订阅，或点击"自定义币种"输入其他币种符号。

**监控内容包括：**
• 📊 技术指标信号（MACD + RSI）
• 💰 价格变化提醒
• ⛓️ 链上大额转账
• 🐋 巨鲸地址活动

选择币种后，您可以设置个性化的提醒阈值。
`;

  await ctx.reply(message, keyboard);
}
