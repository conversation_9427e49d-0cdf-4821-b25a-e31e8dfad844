import { Telegraf } from 'telegraf';
import { BotContextWithSession } from '../index';
import { startCommand } from './start';
import { helpCommand } from './help';
import { subscribeCommand } from './subscribe';
import { unsubscribeCommand } from './unsubscribe';
import { listSubscriptionsCommand } from './list';
import { settingsCommand } from './settings';
import { statusCommand } from './status';
import { priceCommand } from './price';

export function setupCommands(bot: Telegraf<BotContextWithSession>): void {
  // Basic commands
  bot.command('start', startCommand);
  bot.command('help', helpCommand);
  bot.command('status', statusCommand);

  // Subscription management
  bot.command('subscribe', subscribeCommand);
  bot.command('unsubscribe', unsubscribeCommand);
  bot.command('list', listSubscriptionsCommand);
  bot.command('settings', settingsCommand);

  // Market data commands
  bot.command('price', priceCommand);

  // Handle callback queries (inline keyboard responses)
  bot.on('callback_query', async (ctx) => {
    try {
      await ctx.answerCbQuery();
      
      const data = ctx.callbackQuery.data;
      if (!data) return;

      // Route callback queries to appropriate handlers
      if (data.startsWith('subscribe_')) {
        await handleSubscribeCallback(ctx, data);
      } else if (data.startsWith('unsubscribe_')) {
        await handleUnsubscribeCallback(ctx, data);
      } else if (data.startsWith('settings_')) {
        await handleSettingsCallback(ctx, data);
      }
    } catch (error) {
      console.error('Callback query error:', error);
      ctx.reply('处理请求时发生错误。');
    }
  });

  // Handle text messages (for conversation flows)
  bot.on('text', async (ctx) => {
    const text = ctx.message.text;
    const step = ctx.session.step;

    if (step === 'waiting_for_coin_symbol') {
      await handleCoinSymbolInput(ctx, text);
    } else if (step === 'waiting_for_threshold') {
      await handleThresholdInput(ctx, text);
    } else {
      // Default response for unrecognized text
      ctx.reply('我不理解这个命令。请使用 /help 查看可用命令。');
    }
  });
}

// Callback handlers
async function handleSubscribeCallback(ctx: BotContextWithSession, data: string): Promise<void> {
  const coinId = data.replace('subscribe_', '');
  // Implementation will be added when subscription service is ready
  ctx.reply(`订阅 ${coinId} 的功能正在开发中...`);
}

async function handleUnsubscribeCallback(ctx: BotContextWithSession, data: string): Promise<void> {
  const coinId = data.replace('unsubscribe_', '');
  // Implementation will be added when subscription service is ready
  ctx.reply(`取消订阅 ${coinId} 的功能正在开发中...`);
}

async function handleSettingsCallback(ctx: BotContextWithSession, data: string): Promise<void> {
  const setting = data.replace('settings_', '');
  // Implementation will be added when settings service is ready
  ctx.reply(`设置 ${setting} 的功能正在开发中...`);
}

// Text input handlers
async function handleCoinSymbolInput(ctx: BotContextWithSession, text: string): Promise<void> {
  const symbol = text.toUpperCase().trim();
  
  // Validate symbol format
  if (!/^[A-Z]{2,10}$/.test(symbol)) {
    ctx.reply('请输入有效的币种符号（如：BTC, ETH, ADA）');
    return;
  }

  // Store symbol and move to next step
  ctx.session.data = { ...ctx.session.data, symbol };
  ctx.session.step = 'waiting_for_threshold';
  
  ctx.reply(
    `您选择了 ${symbol}。\n\n` +
    '现在请设置价格变化阈值（百分比，如：5 表示5%）：'
  );
}

async function handleThresholdInput(ctx: BotContextWithSession, text: string): Promise<void> {
  const threshold = parseFloat(text.trim());
  
  if (isNaN(threshold) || threshold <= 0 || threshold > 100) {
    ctx.reply('请输入有效的阈值（0-100之间的数字）');
    return;
  }

  // Store threshold and complete subscription
  ctx.session.data = { ...ctx.session.data, threshold };
  
  const { symbol } = ctx.session.data || {};
  
  // Clear session
  ctx.session.step = undefined;
  ctx.session.data = {};
  
  ctx.reply(
    `✅ 订阅设置完成！\n\n` +
    `币种：${symbol}\n` +
    `价格变化阈值：${threshold}%\n\n` +
    '当价格变化超过阈值时，我会通知您。'
  );
}
