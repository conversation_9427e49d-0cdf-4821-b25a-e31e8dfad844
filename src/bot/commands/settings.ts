import { Markup } from 'telegraf';
import { BotContextWithSession } from '../index';

export async function settingsCommand(ctx: BotContextWithSession): Promise<void> {
  const keyboard = Markup.inlineKeyboard([
    [Markup.button.callback('⏰ 推送频率', 'settings_frequency')],
    [Markup.button.callback('📊 技术指标设置', 'settings_indicators')],
    [Markup.button.callback('💰 价格提醒阈值', 'settings_price_threshold')],
    [Markup.button.callback('⛓️ 链上监控设置', 'settings_chain_monitoring')],
    [Markup.button.callback('🔔 通知设置', 'settings_notifications')],
  ]);

  const message = `
⚙️ **个人设置**

请选择要配置的设置项：

**当前设置：**
• 推送频率：每日一次
• 价格阈值：5%
• 技术指标：MACD + RSI
• 链上监控：已启用
• 通知状态：已启用

点击下方按钮修改相应设置：
`;

  await ctx.reply(message, keyboard);
}
