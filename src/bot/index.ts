import { Telegraf, Context, session } from 'telegraf';
import config from '../config';
import logger from '../config/logger';
import { BotContext, User } from '../types';
import { UserService } from '../services/UserService';
import { setupCommands } from './commands';
import { setupMiddleware } from './middleware';

export interface SessionData {
  user?: User;
  step?: string;
  data?: Record<string, any>;
}

export interface BotContextWithSession extends Context {
  session: SessionData;
  botContext: BotContext;
}

class TelegramBot {
  private bot: Telegraf<BotContextWithSession>;
  private userService: UserService;

  constructor() {
    this.bot = new Telegraf<BotContextWithSession>(config.telegram.botToken);
    this.userService = new UserService();
    this.setupBot();
  }

  private setupBot(): void {
    // Setup session middleware
    this.bot.use(session({
      defaultSession: (): SessionData => ({})
    }));

    // Setup custom middleware
    setupMiddleware(this.bot, this.userService);

    // Setup commands
    setupCommands(this.bot);

    // Error handling
    this.bot.catch((err, ctx) => {
      logger.error(`Bot error for ${ctx.updateType}:`, err);
      ctx.reply('抱歉，发生了一个错误。请稍后再试。');
    });

    // Graceful shutdown
    process.once('SIGINT', () => this.stop('SIGINT'));
    process.once('SIGTERM', () => this.stop('SIGTERM'));
  }

  public async start(): Promise<void> {
    try {
      await this.bot.launch();
      logger.info('🤖 Telegram bot started successfully');
    } catch (error) {
      logger.error('Failed to start Telegram bot:', error);
      throw error;
    }
  }

  public async stop(signal?: string): Promise<void> {
    try {
      logger.info(`Stopping bot due to ${signal || 'manual stop'}`);
      this.bot.stop(signal);
      logger.info('🤖 Telegram bot stopped successfully');
    } catch (error) {
      logger.error('Failed to stop Telegram bot:', error);
      throw error;
    }
  }

  public getBot(): Telegraf<BotContextWithSession> {
    return this.bot;
  }
}

export default TelegramBot;
