import { MACD, RSI } from 'technicalindicators';
import { PriceDataModel } from '../models/PriceDataModel';
import { TechnicalSignalModel } from '../models/TechnicalSignalModel';
import { CryptocurrencyModel } from '../models/CryptocurrencyModel';
import { 
  TechnicalIndicators, 
  MACDData, 
  RSIData, 
  SignalType, 
  SignalStrength,
  TechnicalSignal 
} from '../types';
import logger from '../config/logger';

export interface IndicatorInput {
  prices: number[];
  timestamps: Date[];
}

export interface AnalysisResult {
  coinId: string;
  symbol: string;
  indicators: TechnicalIndicators;
  signals: TechnicalSignal[];
  recommendation: {
    action: SignalType;
    strength: SignalStrength;
    confidence: number;
    reasons: string[];
  };
}

export class TechnicalAnalysisService {
  private priceDataModel: PriceDataModel;
  private signalModel: TechnicalSignalModel;
  private cryptoModel: CryptocurrencyModel;

  constructor() {
    this.priceDataModel = new PriceDataModel();
    this.signalModel = new TechnicalSignalModel();
    this.cryptoModel = new CryptocurrencyModel();
  }

  async calculateMACD(prices: number[], fastPeriod = 12, slowPeriod = 26, signalPeriod = 9): Promise<MACDData | null> {
    try {
      if (prices.length < slowPeriod + signalPeriod) {
        logger.warn(`Insufficient data for MACD calculation: ${prices.length} prices, need at least ${slowPeriod + signalPeriod}`);
        return null;
      }

      const macdResult = MACD.calculate({
        values: prices,
        fastPeriod,
        slowPeriod,
        signalPeriod,
        SimpleMAOscillator: false,
        SimpleMASignal: false,
      });

      if (macdResult.length === 0) {
        return null;
      }

      const latest = macdResult[macdResult.length - 1];
      const previous = macdResult.length > 1 ? macdResult[macdResult.length - 2] : null;

      // Check for pulse signal (MACD crosses above signal line and histogram turns positive)
      const isPulseSignal = previous && 
        previous.MACD <= previous.signal && 
        latest.MACD > latest.signal && 
        latest.histogram > 0;

      return {
        macd: latest.MACD || 0,
        signal: latest.signal || 0,
        histogram: latest.histogram || 0,
        isPulseSignal: !!isPulseSignal,
      };
    } catch (error) {
      logger.error('Error calculating MACD:', error);
      return null;
    }
  }

  async calculateRSI(prices: number[], period = 14): Promise<RSIData | null> {
    try {
      if (prices.length < period + 1) {
        logger.warn(`Insufficient data for RSI calculation: ${prices.length} prices, need at least ${period + 1}`);
        return null;
      }

      const rsiResult = RSI.calculate({
        values: prices,
        period,
      });

      if (rsiResult.length === 0) {
        return null;
      }

      const latest = rsiResult[rsiResult.length - 1];

      return {
        rsi: latest,
        isOversold: latest < 30,
        isOverbought: latest > 70,
        isInRange: latest >= 30 && latest <= 70,
      };
    } catch (error) {
      logger.error('Error calculating RSI:', error);
      return null;
    }
  }

  async analyzeSymbol(coinId: string, days = 30): Promise<AnalysisResult | null> {
    try {
      const crypto = await this.cryptoModel.findById(coinId);
      if (!crypto) {
        throw new Error(`Cryptocurrency not found: ${coinId}`);
      }

      // Get historical price data
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - days * 24 * 60 * 60 * 1000);
      
      const priceData = await this.priceDataModel.getPriceDataByTimeRange(coinId, startTime, endTime);
      
      if (priceData.length < 30) {
        logger.warn(`Insufficient price data for analysis: ${priceData.length} records`);
        return null;
      }

      // Extract prices and sort by timestamp
      const sortedData = priceData.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
      const prices = sortedData.map(item => item.price);
      const timestamps = sortedData.map(item => item.timestamp);

      // Calculate indicators
      const macdData = await this.calculateMACD(prices);
      const rsiData = await this.calculateRSI(prices);

      if (!macdData || !rsiData) {
        logger.warn(`Failed to calculate indicators for ${crypto.symbol}`);
        return null;
      }

      const indicators: TechnicalIndicators = {
        coinId,
        macd: macdData,
        rsi: rsiData,
        timestamp: new Date(),
      };

      // Generate signals and recommendation
      const signals = await this.generateSignals(coinId, indicators);
      const recommendation = this.generateRecommendation(indicators, signals);

      return {
        coinId,
        symbol: crypto.symbol,
        indicators,
        signals,
        recommendation,
      };
    } catch (error) {
      logger.error(`Error analyzing symbol ${coinId}:`, error);
      return null;
    }
  }

  private async generateSignals(coinId: string, indicators: TechnicalIndicators): Promise<TechnicalSignal[]> {
    const signals: TechnicalSignal[] = [];

    try {
      // MACD Pulse Signal
      if (indicators.macd.isPulseSignal && indicators.rsi.isInRange) {
        const signal = await this.signalModel.create({
          coinId,
          signalType: SignalType.BUY,
          strength: SignalStrength.STRONG,
          description: 'MACD脉冲信号：MACD线穿越信号线且柱状图转正，RSI在合理区间',
          macdValue: indicators.macd.macd,
          macdSignal: indicators.macd.signal,
          macdHistogram: indicators.macd.histogram,
          rsiValue: indicators.rsi.rsi,
          isPulseSignal: true,
        });
        signals.push(signal);
      }

      // RSI Oversold Signal
      if (indicators.rsi.isOversold && indicators.macd.histogram > 0) {
        const signal = await this.signalModel.create({
          coinId,
          signalType: SignalType.BUY,
          strength: SignalStrength.MODERATE,
          description: 'RSI超卖信号：RSI低于30，MACD柱状图为正',
          macdValue: indicators.macd.macd,
          macdSignal: indicators.macd.signal,
          macdHistogram: indicators.macd.histogram,
          rsiValue: indicators.rsi.rsi,
          isPulseSignal: false,
        });
        signals.push(signal);
      }

      // RSI Overbought Signal
      if (indicators.rsi.isOverbought && indicators.macd.histogram < 0) {
        const signal = await this.signalModel.create({
          coinId,
          signalType: SignalType.SELL,
          strength: SignalStrength.MODERATE,
          description: 'RSI超买信号：RSI高于70，MACD柱状图为负',
          macdValue: indicators.macd.macd,
          macdSignal: indicators.macd.signal,
          macdHistogram: indicators.macd.histogram,
          rsiValue: indicators.rsi.rsi,
          isPulseSignal: false,
        });
        signals.push(signal);
      }

      // MACD Bearish Signal
      if (indicators.macd.macd < indicators.macd.signal && indicators.macd.histogram < 0 && indicators.rsi.rsi < 50) {
        const signal = await this.signalModel.create({
          coinId,
          signalType: SignalType.SELL,
          strength: SignalStrength.WEAK,
          description: 'MACD看跌信号：MACD线低于信号线，柱状图为负，RSI偏弱',
          macdValue: indicators.macd.macd,
          macdSignal: indicators.macd.signal,
          macdHistogram: indicators.macd.histogram,
          rsiValue: indicators.rsi.rsi,
          isPulseSignal: false,
        });
        signals.push(signal);
      }

      // Neutral/Hold Signal
      if (indicators.rsi.isInRange && Math.abs(indicators.macd.histogram) < 0.1) {
        const signal = await this.signalModel.create({
          coinId,
          signalType: SignalType.HOLD,
          strength: SignalStrength.WEAK,
          description: '中性信号：RSI在正常区间，MACD柱状图接近零轴',
          macdValue: indicators.macd.macd,
          macdSignal: indicators.macd.signal,
          macdHistogram: indicators.macd.histogram,
          rsiValue: indicators.rsi.rsi,
          isPulseSignal: false,
        });
        signals.push(signal);
      }

    } catch (error) {
      logger.error('Error generating signals:', error);
    }

    return signals;
  }

  private generateRecommendation(indicators: TechnicalIndicators, signals: TechnicalSignal[]): {
    action: SignalType;
    strength: SignalStrength;
    confidence: number;
    reasons: string[];
  } {
    const reasons: string[] = [];
    let action = SignalType.HOLD;
    let strength = SignalStrength.WEAK;
    let confidence = 0;

    // Analyze MACD
    if (indicators.macd.isPulseSignal) {
      reasons.push('MACD脉冲信号出现');
      confidence += 40;
      action = SignalType.BUY;
      strength = SignalStrength.STRONG;
    } else if (indicators.macd.macd > indicators.macd.signal) {
      reasons.push('MACD线高于信号线');
      confidence += 20;
      if (action === SignalType.HOLD) action = SignalType.BUY;
    } else if (indicators.macd.macd < indicators.macd.signal) {
      reasons.push('MACD线低于信号线');
      confidence += 20;
      if (action === SignalType.HOLD) action = SignalType.SELL;
    }

    // Analyze RSI
    if (indicators.rsi.isOversold) {
      reasons.push('RSI显示超卖状态');
      confidence += 25;
      if (action !== SignalType.SELL) action = SignalType.BUY;
    } else if (indicators.rsi.isOverbought) {
      reasons.push('RSI显示超买状态');
      confidence += 25;
      if (action !== SignalType.BUY) action = SignalType.SELL;
    } else if (indicators.rsi.isInRange) {
      reasons.push('RSI在正常区间');
      confidence += 10;
    }

    // Analyze histogram
    if (indicators.macd.histogram > 0) {
      reasons.push('MACD柱状图为正');
      confidence += 15;
    } else if (indicators.macd.histogram < 0) {
      reasons.push('MACD柱状图为负');
      confidence += 15;
    }

    // Determine strength based on confidence
    if (confidence >= 70) {
      strength = SignalStrength.VERY_STRONG;
    } else if (confidence >= 50) {
      strength = SignalStrength.STRONG;
    } else if (confidence >= 30) {
      strength = SignalStrength.MODERATE;
    } else {
      strength = SignalStrength.WEAK;
    }

    // Cap confidence at 100
    confidence = Math.min(confidence, 100);

    return {
      action,
      strength,
      confidence,
      reasons,
    };
  }

  async analyzeAllSymbols(): Promise<{
    analyzed: number;
    signals: number;
    errors: string[];
  }> {
    try {
      const result = {
        analyzed: 0,
        signals: 0,
        errors: [],
      };

      const cryptos = await this.cryptoModel.findAll(true);
      logger.info(`Starting technical analysis for ${cryptos.length} cryptocurrencies`);

      for (const crypto of cryptos) {
        try {
          const analysis = await this.analyzeSymbol(crypto.id);
          if (analysis) {
            result.analyzed++;
            result.signals += analysis.signals.length;
            
            if (analysis.signals.length > 0) {
              logger.info(`Generated ${analysis.signals.length} signals for ${crypto.symbol}`);
            }
          }
        } catch (error) {
          const errorMsg = `Failed to analyze ${crypto.symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          logger.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      logger.info(`Technical analysis completed: ${result.analyzed} analyzed, ${result.signals} signals generated, ${result.errors.length} errors`);
      return result;

    } catch (error) {
      logger.error('Error in analyzeAllSymbols:', error);
      throw error;
    }
  }

  async getRecentSignals(hours = 24): Promise<TechnicalSignal[]> {
    try {
      return await this.signalModel.findRecentSignals(hours);
    } catch (error) {
      logger.error('Error getting recent signals:', error);
      throw error;
    }
  }

  async getPulseSignals(hours = 24): Promise<TechnicalSignal[]> {
    try {
      return await this.signalModel.findPulseSignals(hours);
    } catch (error) {
      logger.error('Error getting pulse signals:', error);
      throw error;
    }
  }

  async getStrongSignals(hours = 24): Promise<TechnicalSignal[]> {
    try {
      return await this.signalModel.findStrongSignals(hours);
    } catch (error) {
      logger.error('Error getting strong signals:', error);
      throw error;
    }
  }
}
