import { UserService } from './UserService';
import { User } from '../types';
import logger from '../config/logger';

export enum Permission {
  // Basic permissions
  VIEW_PRICES = 'view_prices',
  SUBSCRIBE_COINS = 'subscribe_coins',
  VIEW_TECHNICAL_SIGNALS = 'view_technical_signals',
  
  // Premium permissions
  UNLIMITED_SUBSCRIPTIONS = 'unlimited_subscriptions',
  ADVANCED_ALERTS = 'advanced_alerts',
  CUSTOM_INDICATORS = 'custom_indicators',
  PRIORITY_SUPPORT = 'priority_support',
  EXPORT_DATA = 'export_data',
  
  // Admin permissions
  MANAGE_USERS = 'manage_users',
  VIEW_ANALYTICS = 'view_analytics',
  MANAGE_SYSTEM = 'manage_system',
  SEND_BROADCASTS = 'send_broadcasts',
}

export enum Role {
  FREE_USER = 'free_user',
  PREMIUM_USER = 'premium_user',
  ADMIN = 'admin',
  SUPER_ADMIN = 'super_admin',
}

export interface UserPermissions {
  userId: string;
  role: Role;
  permissions: Permission[];
  subscriptionLimits: {
    maxSubscriptions: number;
    maxAlertsPerDay: number;
    canUseAdvancedFeatures: boolean;
  };
}

export class AuthorizationService {
  private userService: UserService;
  private adminUserIds: Set<string>;

  constructor() {
    this.userService = new UserService();
    this.adminUserIds = new Set(); // In production, this would come from config or database
  }

  private getRolePermissions(role: Role): Permission[] {
    switch (role) {
      case Role.FREE_USER:
        return [
          Permission.VIEW_PRICES,
          Permission.SUBSCRIBE_COINS,
          Permission.VIEW_TECHNICAL_SIGNALS,
        ];
      
      case Role.PREMIUM_USER:
        return [
          ...this.getRolePermissions(Role.FREE_USER),
          Permission.UNLIMITED_SUBSCRIPTIONS,
          Permission.ADVANCED_ALERTS,
          Permission.CUSTOM_INDICATORS,
          Permission.PRIORITY_SUPPORT,
          Permission.EXPORT_DATA,
        ];
      
      case Role.ADMIN:
        return [
          ...this.getRolePermissions(Role.PREMIUM_USER),
          Permission.MANAGE_USERS,
          Permission.VIEW_ANALYTICS,
          Permission.SEND_BROADCASTS,
        ];
      
      case Role.SUPER_ADMIN:
        return [
          ...this.getRolePermissions(Role.ADMIN),
          Permission.MANAGE_SYSTEM,
        ];
      
      default:
        return [];
    }
  }

  private getUserRole(user: User): Role {
    // Check if user is admin
    if (this.adminUserIds.has(user.id)) {
      return Role.SUPER_ADMIN;
    }

    // Check if user is premium
    if (user.isPremium) {
      return Role.PREMIUM_USER;
    }

    // Default to free user
    return Role.FREE_USER;
  }

  async getUserPermissions(userId: string): Promise<UserPermissions> {
    try {
      const user = await this.userService.getUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      const role = this.getUserRole(user);
      const permissions = this.getRolePermissions(role);

      const subscriptionLimits = this.getSubscriptionLimits(role);

      return {
        userId,
        role,
        permissions,
        subscriptionLimits,
      };

    } catch (error) {
      logger.error('Error getting user permissions:', error);
      throw error;
    }
  }

  private getSubscriptionLimits(role: Role): {
    maxSubscriptions: number;
    maxAlertsPerDay: number;
    canUseAdvancedFeatures: boolean;
  } {
    switch (role) {
      case Role.FREE_USER:
        return {
          maxSubscriptions: 5,
          maxAlertsPerDay: 10,
          canUseAdvancedFeatures: false,
        };
      
      case Role.PREMIUM_USER:
        return {
          maxSubscriptions: 100,
          maxAlertsPerDay: 100,
          canUseAdvancedFeatures: true,
        };
      
      case Role.ADMIN:
      case Role.SUPER_ADMIN:
        return {
          maxSubscriptions: 1000,
          maxAlertsPerDay: 1000,
          canUseAdvancedFeatures: true,
        };
      
      default:
        return {
          maxSubscriptions: 0,
          maxAlertsPerDay: 0,
          canUseAdvancedFeatures: false,
        };
    }
  }

  async hasPermission(userId: string, permission: Permission): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return userPermissions.permissions.includes(permission);
    } catch (error) {
      logger.error('Error checking permission:', error);
      return false;
    }
  }

  async hasAnyPermission(userId: string, permissions: Permission[]): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return permissions.some(permission => userPermissions.permissions.includes(permission));
    } catch (error) {
      logger.error('Error checking any permission:', error);
      return false;
    }
  }

  async hasAllPermissions(userId: string, permissions: Permission[]): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return permissions.every(permission => userPermissions.permissions.includes(permission));
    } catch (error) {
      logger.error('Error checking all permissions:', error);
      return false;
    }
  }

  async canSubscribeToMoreCoins(userId: string, currentSubscriptionCount: number): Promise<{
    canSubscribe: boolean;
    reason?: string;
    maxAllowed: number;
  }> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      const maxAllowed = userPermissions.subscriptionLimits.maxSubscriptions;
      const canSubscribe = currentSubscriptionCount < maxAllowed;

      return {
        canSubscribe,
        reason: canSubscribe ? undefined : `Maximum subscriptions reached (${maxAllowed})`,
        maxAllowed,
      };

    } catch (error) {
      logger.error('Error checking subscription limit:', error);
      return {
        canSubscribe: false,
        reason: 'Error checking permissions',
        maxAllowed: 0,
      };
    }
  }

  async canReceiveMoreAlerts(userId: string, alertsReceivedToday: number): Promise<{
    canReceive: boolean;
    reason?: string;
    maxAllowed: number;
  }> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      const maxAllowed = userPermissions.subscriptionLimits.maxAlertsPerDay;
      const canReceive = alertsReceivedToday < maxAllowed;

      return {
        canReceive,
        reason: canReceive ? undefined : `Daily alert limit reached (${maxAllowed})`,
        maxAllowed,
      };

    } catch (error) {
      logger.error('Error checking alert limit:', error);
      return {
        canReceive: false,
        reason: 'Error checking permissions',
        maxAllowed: 0,
      };
    }
  }

  async promoteUserToPremium(userId: string): Promise<User> {
    try {
      const user = await this.userService.updateUser(userId, { isPremium: true });
      logger.info(`User ${userId} promoted to premium`);
      return user;
    } catch (error) {
      logger.error('Error promoting user to premium:', error);
      throw error;
    }
  }

  async demoteUserFromPremium(userId: string): Promise<User> {
    try {
      const user = await this.userService.updateUser(userId, { isPremium: false });
      logger.info(`User ${userId} demoted from premium`);
      return user;
    } catch (error) {
      logger.error('Error demoting user from premium:', error);
      throw error;
    }
  }

  async addAdmin(userId: string): Promise<void> {
    try {
      this.adminUserIds.add(userId);
      logger.info(`User ${userId} added as admin`);
    } catch (error) {
      logger.error('Error adding admin:', error);
      throw error;
    }
  }

  async removeAdmin(userId: string): Promise<void> {
    try {
      this.adminUserIds.delete(userId);
      logger.info(`User ${userId} removed as admin`);
    } catch (error) {
      logger.error('Error removing admin:', error);
      throw error;
    }
  }

  async isAdmin(userId: string): Promise<boolean> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return userPermissions.role === Role.ADMIN || userPermissions.role === Role.SUPER_ADMIN;
    } catch (error) {
      logger.error('Error checking admin status:', error);
      return false;
    }
  }

  async isPremium(userId: string): Promise<boolean> {
    try {
      const user = await this.userService.getUserById(userId);
      return user?.isPremium || false;
    } catch (error) {
      logger.error('Error checking premium status:', error);
      return false;
    }
  }

  async getUserRole(userId: string): Promise<Role> {
    try {
      const userPermissions = await this.getUserPermissions(userId);
      return userPermissions.role;
    } catch (error) {
      logger.error('Error getting user role:', error);
      return Role.FREE_USER;
    }
  }

  async getAllPermissions(): Promise<Permission[]> {
    return Object.values(Permission);
  }

  async getAllRoles(): Promise<Role[]> {
    return Object.values(Role);
  }

  async getRoleDescription(role: Role): Promise<string> {
    switch (role) {
      case Role.FREE_USER:
        return '免费用户 - 基础功能访问权限';
      case Role.PREMIUM_USER:
        return '高级用户 - 完整功能访问权限';
      case Role.ADMIN:
        return '管理员 - 用户管理和系统监控权限';
      case Role.SUPER_ADMIN:
        return '超级管理员 - 完整系统管理权限';
      default:
        return '未知角色';
    }
  }

  async getPermissionDescription(permission: Permission): Promise<string> {
    const descriptions: Record<Permission, string> = {
      [Permission.VIEW_PRICES]: '查看价格数据',
      [Permission.SUBSCRIBE_COINS]: '订阅加密货币',
      [Permission.VIEW_TECHNICAL_SIGNALS]: '查看技术指标信号',
      [Permission.UNLIMITED_SUBSCRIPTIONS]: '无限制订阅',
      [Permission.ADVANCED_ALERTS]: '高级提醒功能',
      [Permission.CUSTOM_INDICATORS]: '自定义技术指标',
      [Permission.PRIORITY_SUPPORT]: '优先客户支持',
      [Permission.EXPORT_DATA]: '导出数据',
      [Permission.MANAGE_USERS]: '管理用户',
      [Permission.VIEW_ANALYTICS]: '查看分析数据',
      [Permission.MANAGE_SYSTEM]: '系统管理',
      [Permission.SEND_BROADCASTS]: '发送广播消息',
    };

    return descriptions[permission] || '未知权限';
  }
}
