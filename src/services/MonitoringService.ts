import axios from 'axios';
import { checkDatabaseHealth } from '../config/database';
import { CoinGeckoService } from './CoinGeckoService';
import { EtherscanService } from './EtherscanService';
import { BSCScanService } from './BSCScanService';
import config from '../config';
import logger from '../config/logger';

export interface HealthCheckResult {
  service: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime: number;
  error?: string;
  details?: Record<string, any>;
}

export interface SystemHealth {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: Date;
  uptime: number;
  services: HealthCheckResult[];
  metrics: SystemMetrics;
}

export interface SystemMetrics {
  memoryUsage: {
    used: number;
    total: number;
    percentage: number;
  };
  cpuUsage: number;
  activeConnections: number;
  requestsPerMinute: number;
  errorRate: number;
}

export interface AlertConfig {
  webhookUrl?: string;
  emailRecipients?: string[];
  slackWebhook?: string;
  enableAlerts: boolean;
}

export class MonitoringService {
  private coinGeckoService: CoinGeckoService;
  private etherscanService: EtherscanService;
  private bscScanService: BSCScanService;
  private alertConfig: AlertConfig;
  private requestCount: number = 0;
  private errorCount: number = 0;
  private lastMinuteRequests: number[] = [];

  constructor() {
    this.coinGeckoService = new CoinGeckoService();
    this.etherscanService = new EtherscanService();
    this.bscScanService = new BSCScanService();
    
    this.alertConfig = {
      webhookUrl: config.monitoring.webhookUrl,
      enableAlerts: true,
    };

    // Track requests per minute
    setInterval(() => {
      this.lastMinuteRequests.push(this.requestCount);
      if (this.lastMinuteRequests.length > 60) {
        this.lastMinuteRequests.shift();
      }
      this.requestCount = 0;
    }, 1000);
  }

  public incrementRequestCount(): void {
    this.requestCount++;
  }

  public incrementErrorCount(): void {
    this.errorCount++;
  }

  async performHealthCheck(): Promise<SystemHealth> {
    const startTime = Date.now();
    const services: HealthCheckResult[] = [];

    // Check database
    services.push(await this.checkDatabase());

    // Check external APIs
    services.push(await this.checkCoinGeckoAPI());
    services.push(await this.checkEtherscanAPI());
    services.push(await this.checkBSCAPI());

    // Check Telegram Bot API
    services.push(await this.checkTelegramAPI());

    // Determine overall health
    const unhealthyServices = services.filter(s => s.status === 'unhealthy');
    const degradedServices = services.filter(s => s.status === 'degraded');

    let overall: 'healthy' | 'unhealthy' | 'degraded';
    if (unhealthyServices.length > 0) {
      overall = 'unhealthy';
    } else if (degradedServices.length > 0) {
      overall = 'degraded';
    } else {
      overall = 'healthy';
    }

    const metrics = this.getSystemMetrics();

    const health: SystemHealth = {
      overall,
      timestamp: new Date(),
      uptime: process.uptime(),
      services,
      metrics,
    };

    // Send alerts if necessary
    if (overall !== 'healthy') {
      await this.sendHealthAlert(health);
    }

    logger.info(`Health check completed in ${Date.now() - startTime}ms - Status: ${overall}`);
    return health;
  }

  private async checkDatabase(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const isHealthy = await checkDatabaseHealth();
      const responseTime = Date.now() - startTime;

      return {
        service: 'database',
        status: isHealthy ? 'healthy' : 'unhealthy',
        responseTime,
        details: {
          connectionPool: 'active',
        },
      };
    } catch (error) {
      return {
        service: 'database',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkCoinGeckoAPI(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      await this.coinGeckoService.getSimplePrice(['bitcoin'], ['usd']);
      const responseTime = Date.now() - startTime;

      return {
        service: 'coingecko',
        status: responseTime < 5000 ? 'healthy' : 'degraded',
        responseTime,
        details: {
          endpoint: 'simple/price',
        },
      };
    } catch (error) {
      return {
        service: 'coingecko',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkEtherscanAPI(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      await this.etherscanService.getLatestBlock();
      const responseTime = Date.now() - startTime;

      return {
        service: 'etherscan',
        status: responseTime < 3000 ? 'healthy' : 'degraded',
        responseTime,
        details: {
          endpoint: 'latest_block',
        },
      };
    } catch (error) {
      return {
        service: 'etherscan',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkBSCAPI(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      await this.bscScanService.getLatestBlock();
      const responseTime = Date.now() - startTime;

      return {
        service: 'bscscan',
        status: responseTime < 3000 ? 'healthy' : 'degraded',
        responseTime,
        details: {
          endpoint: 'latest_block',
        },
      };
    } catch (error) {
      return {
        service: 'bscscan',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private async checkTelegramAPI(): Promise<HealthCheckResult> {
    const startTime = Date.now();
    
    try {
      const response = await axios.get(
        `https://api.telegram.org/bot${config.telegram.botToken}/getMe`,
        { timeout: 5000 }
      );

      const responseTime = Date.now() - startTime;

      return {
        service: 'telegram',
        status: response.data.ok ? 'healthy' : 'unhealthy',
        responseTime,
        details: {
          botUsername: response.data.result?.username,
        },
      };
    } catch (error) {
      return {
        service: 'telegram',
        status: 'unhealthy',
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }

  private getSystemMetrics(): SystemMetrics {
    const memUsage = process.memoryUsage();
    const totalMemory = memUsage.heapTotal + memUsage.external;
    const usedMemory = memUsage.heapUsed;

    const requestsPerMinute = this.lastMinuteRequests.reduce((sum, count) => sum + count, 0);
    const errorRate = requestsPerMinute > 0 ? (this.errorCount / requestsPerMinute) * 100 : 0;

    return {
      memoryUsage: {
        used: usedMemory,
        total: totalMemory,
        percentage: (usedMemory / totalMemory) * 100,
      },
      cpuUsage: process.cpuUsage().user / 1000000, // Convert to seconds
      activeConnections: 0, // Would need to track this separately
      requestsPerMinute,
      errorRate,
    };
  }

  private async sendHealthAlert(health: SystemHealth): Promise<void> {
    if (!this.alertConfig.enableAlerts) {
      return;
    }

    const alertMessage = this.createAlertMessage(health);

    try {
      // Send to webhook if configured
      if (this.alertConfig.webhookUrl) {
        await this.sendWebhookAlert(alertMessage);
      }

      logger.warn('Health alert sent:', alertMessage);
    } catch (error) {
      logger.error('Failed to send health alert:', error);
    }
  }

  private createAlertMessage(health: SystemHealth): string {
    const unhealthyServices = health.services.filter(s => s.status === 'unhealthy');
    const degradedServices = health.services.filter(s => s.status === 'degraded');

    let message = `🚨 System Health Alert - Status: ${health.overall.toUpperCase()}\n\n`;
    
    if (unhealthyServices.length > 0) {
      message += `❌ Unhealthy Services:\n`;
      unhealthyServices.forEach(service => {
        message += `• ${service.service}: ${service.error || 'Unknown error'}\n`;
      });
      message += '\n';
    }

    if (degradedServices.length > 0) {
      message += `⚠️ Degraded Services:\n`;
      degradedServices.forEach(service => {
        message += `• ${service.service}: Response time ${service.responseTime}ms\n`;
      });
      message += '\n';
    }

    message += `📊 System Metrics:\n`;
    message += `• Memory Usage: ${health.metrics.memoryUsage.percentage.toFixed(1)}%\n`;
    message += `• Requests/min: ${health.metrics.requestsPerMinute}\n`;
    message += `• Error Rate: ${health.metrics.errorRate.toFixed(2)}%\n`;
    message += `• Uptime: ${Math.floor(health.uptime / 3600)}h ${Math.floor((health.uptime % 3600) / 60)}m\n`;
    message += `\nTimestamp: ${health.timestamp.toISOString()}`;

    return message;
  }

  private async sendWebhookAlert(message: string): Promise<void> {
    if (!this.alertConfig.webhookUrl) {
      return;
    }

    try {
      await axios.post(this.alertConfig.webhookUrl, {
        text: message,
        timestamp: new Date().toISOString(),
        severity: 'warning',
      }, {
        timeout: 5000,
        headers: {
          'Content-Type': 'application/json',
        },
      });
    } catch (error) {
      logger.error('Failed to send webhook alert:', error);
    }
  }

  public async sendCustomAlert(title: string, message: string, severity: 'info' | 'warning' | 'error' = 'info'): Promise<void> {
    if (!this.alertConfig.enableAlerts) {
      return;
    }

    const alertMessage = `${this.getSeverityEmoji(severity)} ${title}\n\n${message}\n\nTimestamp: ${new Date().toISOString()}`;

    try {
      if (this.alertConfig.webhookUrl) {
        await axios.post(this.alertConfig.webhookUrl, {
          text: alertMessage,
          timestamp: new Date().toISOString(),
          severity,
        });
      }

      logger.info('Custom alert sent:', { title, severity });
    } catch (error) {
      logger.error('Failed to send custom alert:', error);
    }
  }

  private getSeverityEmoji(severity: string): string {
    switch (severity) {
      case 'error':
        return '🚨';
      case 'warning':
        return '⚠️';
      case 'info':
        return 'ℹ️';
      default:
        return '📢';
    }
  }

  public getHealthSummary(): {
    uptime: number;
    requestsPerMinute: number;
    errorRate: number;
    memoryUsage: number;
  } {
    const metrics = this.getSystemMetrics();
    
    return {
      uptime: process.uptime(),
      requestsPerMinute: metrics.requestsPerMinute,
      errorRate: metrics.errorRate,
      memoryUsage: metrics.memoryUsage.percentage,
    };
  }

  public enableAlerts(): void {
    this.alertConfig.enableAlerts = true;
    logger.info('Monitoring alerts enabled');
  }

  public disableAlerts(): void {
    this.alertConfig.enableAlerts = false;
    logger.info('Monitoring alerts disabled');
  }
}
