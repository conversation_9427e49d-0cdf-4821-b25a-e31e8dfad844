import { CoinGeckoService } from './CoinGeckoService';
import { CryptocurrencyModel } from '../models/CryptocurrencyModel';
import { Blockchain } from '../types';
import logger from '../config/logger';

export interface CryptocurrencyInitData {
  symbol: string;
  name: string;
  coinGeckoId: string;
  contractAddress?: string;
  blockchain: Blockchain;
}

export class DataInitializationService {
  private coinGeckoService: CoinGeckoService;
  private cryptoModel: CryptocurrencyModel;

  constructor() {
    this.coinGeckoService = new CoinGeckoService();
    this.cryptoModel = new CryptocurrencyModel();
  }

  // Popular cryptocurrencies to initialize
  private getInitialCryptocurrencies(): CryptocurrencyInitData[] {
    return [
      // Bitcoin and major cryptocurrencies
      { symbol: 'BTC', name: 'Bitcoin', coinGeckoId: 'bitcoin', blockchain: Blockchain.ETHEREUM },
      { symbol: 'ETH', name: 'Ethereum', coinGeckoId: 'ethereum', blockchain: Blockchain.ETHEREUM },
      { symbol: 'BNB', name: 'BNB', coinGeckoId: 'binancecoin', blockchain: Blockchain.BSC },
      { symbol: 'ADA', name: 'Cardano', coinGeckoId: 'cardano', blockchain: Blockchain.ETHEREUM },
      { symbol: 'SOL', name: 'Solana', coinGeckoId: 'solana', blockchain: Blockchain.ETHEREUM },
      { symbol: 'XRP', name: 'XRP', coinGeckoId: 'ripple', blockchain: Blockchain.ETHEREUM },
      { symbol: 'DOT', name: 'Polkadot', coinGeckoId: 'polkadot', blockchain: Blockchain.ETHEREUM },
      { symbol: 'DOGE', name: 'Dogecoin', coinGeckoId: 'dogecoin', blockchain: Blockchain.ETHEREUM },
      { symbol: 'AVAX', name: 'Avalanche', coinGeckoId: 'avalanche-2', blockchain: Blockchain.AVALANCHE },
      { symbol: 'SHIB', name: 'Shiba Inu', coinGeckoId: 'shiba-inu', blockchain: Blockchain.ETHEREUM },
      
      // DeFi tokens
      { symbol: 'UNI', name: 'Uniswap', coinGeckoId: 'uniswap', blockchain: Blockchain.ETHEREUM },
      { symbol: 'LINK', name: 'Chainlink', coinGeckoId: 'chainlink', blockchain: Blockchain.ETHEREUM },
      { symbol: 'AAVE', name: 'Aave', coinGeckoId: 'aave', blockchain: Blockchain.ETHEREUM },
      { symbol: 'COMP', name: 'Compound', coinGeckoId: 'compound-governance-token', blockchain: Blockchain.ETHEREUM },
      { symbol: 'MKR', name: 'Maker', coinGeckoId: 'maker', blockchain: Blockchain.ETHEREUM },
      { symbol: 'SUSHI', name: 'SushiSwap', coinGeckoId: 'sushi', blockchain: Blockchain.ETHEREUM },
      
      // Layer 2 and scaling solutions
      { symbol: 'MATIC', name: 'Polygon', coinGeckoId: 'matic-network', blockchain: Blockchain.POLYGON },
      { symbol: 'OP', name: 'Optimism', coinGeckoId: 'optimism', blockchain: Blockchain.OPTIMISM },
      { symbol: 'ARB', name: 'Arbitrum', coinGeckoId: 'arbitrum', blockchain: Blockchain.ARBITRUM },
      
      // Stablecoins
      { symbol: 'USDT', name: 'Tether', coinGeckoId: 'tether', blockchain: Blockchain.ETHEREUM },
      { symbol: 'USDC', name: 'USD Coin', coinGeckoId: 'usd-coin', blockchain: Blockchain.ETHEREUM },
      { symbol: 'BUSD', name: 'Binance USD', coinGeckoId: 'binance-usd', blockchain: Blockchain.BSC },
      { symbol: 'DAI', name: 'Dai', coinGeckoId: 'dai', blockchain: Blockchain.ETHEREUM },
      
      // Other popular tokens
      { symbol: 'LTC', name: 'Litecoin', coinGeckoId: 'litecoin', blockchain: Blockchain.ETHEREUM },
      { symbol: 'BCH', name: 'Bitcoin Cash', coinGeckoId: 'bitcoin-cash', blockchain: Blockchain.ETHEREUM },
      { symbol: 'ETC', name: 'Ethereum Classic', coinGeckoId: 'ethereum-classic', blockchain: Blockchain.ETHEREUM },
      { symbol: 'XLM', name: 'Stellar', coinGeckoId: 'stellar', blockchain: Blockchain.ETHEREUM },
      { symbol: 'VET', name: 'VeChain', coinGeckoId: 'vechain', blockchain: Blockchain.ETHEREUM },
      { symbol: 'TRX', name: 'TRON', coinGeckoId: 'tron', blockchain: Blockchain.ETHEREUM },
      { symbol: 'FIL', name: 'Filecoin', coinGeckoId: 'filecoin', blockchain: Blockchain.ETHEREUM },
      { symbol: 'ATOM', name: 'Cosmos', coinGeckoId: 'cosmos', blockchain: Blockchain.ETHEREUM },
    ];
  }

  async initializeCryptocurrencies(): Promise<{
    created: number;
    skipped: number;
    errors: string[];
  }> {
    try {
      const result = {
        created: 0,
        skipped: 0,
        errors: [],
      };

      const cryptosToInit = this.getInitialCryptocurrencies();
      logger.info(`Initializing ${cryptosToInit.length} cryptocurrencies...`);

      for (const cryptoData of cryptosToInit) {
        try {
          // Check if cryptocurrency already exists
          const existing = await this.cryptoModel.findBySymbol(cryptoData.symbol);
          if (existing) {
            logger.debug(`Cryptocurrency ${cryptoData.symbol} already exists, skipping`);
            result.skipped++;
            continue;
          }

          // Verify the coin exists on CoinGecko
          try {
            await this.coinGeckoService.getCoinById(cryptoData.coinGeckoId, false);
          } catch (error) {
            logger.warn(`CoinGecko ID ${cryptoData.coinGeckoId} not found, skipping ${cryptoData.symbol}`);
            result.errors.push(`CoinGecko ID not found: ${cryptoData.coinGeckoId}`);
            continue;
          }

          // Create the cryptocurrency
          await this.cryptoModel.create({
            symbol: cryptoData.symbol,
            name: cryptoData.name,
            coinGeckoId: cryptoData.coinGeckoId,
            contractAddress: cryptoData.contractAddress,
            blockchain: cryptoData.blockchain,
          });

          result.created++;
          logger.info(`Created cryptocurrency: ${cryptoData.symbol} (${cryptoData.name})`);

        } catch (error) {
          const errorMsg = `Failed to create ${cryptoData.symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          logger.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      logger.info(`Cryptocurrency initialization completed: ${result.created} created, ${result.skipped} skipped, ${result.errors.length} errors`);
      return result;

    } catch (error) {
      logger.error('Error in cryptocurrency initialization:', error);
      throw error;
    }
  }

  async updateCryptocurrencyData(): Promise<{
    updated: number;
    errors: string[];
  }> {
    try {
      const result = {
        updated: 0,
        errors: [],
      };

      // Get all cryptocurrencies from database
      const cryptos = await this.cryptoModel.findAll(true);
      logger.info(`Updating data for ${cryptos.length} cryptocurrencies...`);

      // Get detailed data from CoinGecko
      for (const crypto of cryptos) {
        try {
          const coinData = await this.coinGeckoService.getCoinById(crypto.coinGeckoId, true);
          
          // Update cryptocurrency data if needed
          const updateData: any = {};
          
          if (coinData.name && coinData.name !== crypto.name) {
            updateData.name = coinData.name;
          }

          // Update contract addresses if available
          if (coinData.platforms && Object.keys(coinData.platforms).length > 0) {
            const platforms = coinData.platforms;
            let contractAddress = crypto.contractAddress;

            // Try to get contract address based on blockchain
            switch (crypto.blockchain) {
              case Blockchain.ETHEREUM:
                contractAddress = platforms.ethereum || contractAddress;
                break;
              case Blockchain.BSC:
                contractAddress = platforms['binance-smart-chain'] || contractAddress;
                break;
              case Blockchain.POLYGON:
                contractAddress = platforms['polygon-pos'] || contractAddress;
                break;
              case Blockchain.ARBITRUM:
                contractAddress = platforms.arbitrum || contractAddress;
                break;
              case Blockchain.OPTIMISM:
                contractAddress = platforms.optimism || contractAddress;
                break;
            }

            if (contractAddress && contractAddress !== crypto.contractAddress) {
              updateData.contractAddress = contractAddress;
            }
          }

          if (Object.keys(updateData).length > 0) {
            await this.cryptoModel.update(crypto.id, updateData);
            result.updated++;
            logger.info(`Updated cryptocurrency: ${crypto.symbol}`);
          }

        } catch (error) {
          const errorMsg = `Failed to update ${crypto.symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          logger.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      logger.info(`Cryptocurrency data update completed: ${result.updated} updated, ${result.errors.length} errors`);
      return result;

    } catch (error) {
      logger.error('Error in cryptocurrency data update:', error);
      throw error;
    }
  }

  async addTopCryptocurrencies(limit: number = 100): Promise<{
    created: number;
    skipped: number;
    errors: string[];
  }> {
    try {
      const result = {
        created: 0,
        skipped: 0,
        errors: [],
      };

      logger.info(`Adding top ${limit} cryptocurrencies from CoinGecko...`);

      const topCoins = await this.coinGeckoService.getTopCoins(limit);

      for (const coinData of topCoins) {
        try {
          // Check if cryptocurrency already exists
          const existing = await this.cryptoModel.findByCoinGeckoId(coinData.id);
          if (existing) {
            result.skipped++;
            continue;
          }

          // Determine blockchain (simplified logic)
          let blockchain = Blockchain.ETHEREUM; // Default
          
          // You could enhance this logic based on the coin's characteristics
          if (coinData.symbol.toLowerCase() === 'bnb') {
            blockchain = Blockchain.BSC;
          } else if (coinData.symbol.toLowerCase() === 'matic') {
            blockchain = Blockchain.POLYGON;
          } else if (coinData.symbol.toLowerCase() === 'avax') {
            blockchain = Blockchain.AVALANCHE;
          }

          // Create the cryptocurrency
          await this.cryptoModel.create({
            symbol: coinData.symbol.toUpperCase(),
            name: coinData.name,
            coinGeckoId: coinData.id,
            blockchain,
          });

          result.created++;
          logger.info(`Added cryptocurrency: ${coinData.symbol.toUpperCase()} (${coinData.name})`);

        } catch (error) {
          const errorMsg = `Failed to add ${coinData.symbol}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          logger.error(errorMsg);
          result.errors.push(errorMsg);
        }
      }

      logger.info(`Top cryptocurrencies addition completed: ${result.created} created, ${result.skipped} skipped, ${result.errors.length} errors`);
      return result;

    } catch (error) {
      logger.error('Error adding top cryptocurrencies:', error);
      throw error;
    }
  }
}
