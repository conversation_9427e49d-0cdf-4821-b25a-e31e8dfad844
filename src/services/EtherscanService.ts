import axios, { AxiosInstance } from 'axios';
import config from '../config';
import logger from '../config/logger';
import { EtherscanTransaction, Blockchain } from '../types';

export interface EtherscanBalance {
  account: string;
  balance: string;
}

export interface EtherscanTokenTransfer {
  blockNumber: string;
  timeStamp: string;
  hash: string;
  from: string;
  to: string;
  value: string;
  contractAddress: string;
  tokenName: string;
  tokenSymbol: string;
  tokenDecimal: string;
  transactionIndex: string;
  gas: string;
  gasPrice: string;
  gasUsed: string;
  cumulativeGasUsed: string;
  input: string;
  confirmations: string;
}

export class EtherscanService {
  private client: AxiosInstance;
  private apiKey: string;
  private baseURL = 'https://api.etherscan.io/api';
  private rateLimitDelay = 200; // 5 requests per second for free tier

  constructor() {
    this.apiKey = config.apiKeys.etherscan;
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      params: {
        apikey: this.apiKey,
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor for rate limiting
    this.client.interceptors.request.use(async (config) => {
      await this.delay(this.rateLimitDelay);
      return config;
    });

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => {
        if (response.data.status === '0' && response.data.message !== 'No transactions found') {
          throw new Error(response.data.result || 'Etherscan API error');
        }
        return response;
      },
      (error) => {
        logger.error('Etherscan API error:', error.message);
        throw error;
      }
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getAccountBalance(address: string): Promise<string> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'account',
          action: 'balance',
          address,
          tag: 'latest',
        },
      });

      return response.data.result;
    } catch (error) {
      logger.error(`Error getting balance for ${address}:`, error);
      throw error;
    }
  }

  async getMultipleBalances(addresses: string[]): Promise<EtherscanBalance[]> {
    try {
      if (addresses.length > 20) {
        throw new Error('Maximum 20 addresses allowed per request');
      }

      const response = await this.client.get('', {
        params: {
          module: 'account',
          action: 'balancemulti',
          address: addresses.join(','),
          tag: 'latest',
        },
      });

      return response.data.result;
    } catch (error) {
      logger.error('Error getting multiple balances:', error);
      throw error;
    }
  }

  async getTransactions(
    address: string,
    startBlock = 0,
    endBlock = ********,
    page = 1,
    offset = 100
  ): Promise<EtherscanTransaction[]> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'account',
          action: 'txlist',
          address,
          startblock: startBlock,
          endblock: endBlock,
          page,
          offset: Math.min(offset, 10000), // Max 10000
          sort: 'desc',
        },
      });

      return response.data.result || [];
    } catch (error) {
      logger.error(`Error getting transactions for ${address}:`, error);
      throw error;
    }
  }

  async getInternalTransactions(
    address: string,
    startBlock = 0,
    endBlock = ********,
    page = 1,
    offset = 100
  ): Promise<EtherscanTransaction[]> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'account',
          action: 'txlistinternal',
          address,
          startblock: startBlock,
          endblock: endBlock,
          page,
          offset: Math.min(offset, 10000),
          sort: 'desc',
        },
      });

      return response.data.result || [];
    } catch (error) {
      logger.error(`Error getting internal transactions for ${address}:`, error);
      throw error;
    }
  }

  async getTokenTransfers(
    contractAddress?: string,
    address?: string,
    startBlock = 0,
    endBlock = ********,
    page = 1,
    offset = 100
  ): Promise<EtherscanTokenTransfer[]> {
    try {
      const params: any = {
        module: 'account',
        action: 'tokentx',
        startblock: startBlock,
        endblock: endBlock,
        page,
        offset: Math.min(offset, 10000),
        sort: 'desc',
      };

      if (contractAddress) {
        params.contractaddress = contractAddress;
      }

      if (address) {
        params.address = address;
      }

      const response = await this.client.get('', { params });

      return response.data.result || [];
    } catch (error) {
      logger.error('Error getting token transfers:', error);
      throw error;
    }
  }

  async getTransactionByHash(txHash: string): Promise<any> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'proxy',
          action: 'eth_getTransactionByHash',
          txhash: txHash,
        },
      });

      return response.data.result;
    } catch (error) {
      logger.error(`Error getting transaction ${txHash}:`, error);
      throw error;
    }
  }

  async getTransactionReceipt(txHash: string): Promise<any> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'proxy',
          action: 'eth_getTransactionReceipt',
          txhash: txHash,
        },
      });

      return response.data.result;
    } catch (error) {
      logger.error(`Error getting transaction receipt ${txHash}:`, error);
      throw error;
    }
  }

  async getLatestBlock(): Promise<number> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'proxy',
          action: 'eth_blockNumber',
        },
      });

      return parseInt(response.data.result, 16);
    } catch (error) {
      logger.error('Error getting latest block:', error);
      throw error;
    }
  }

  async getBlockByNumber(blockNumber: number): Promise<any> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'proxy',
          action: 'eth_getBlockByNumber',
          tag: `0x${blockNumber.toString(16)}`,
          boolean: true,
        },
      });

      return response.data.result;
    } catch (error) {
      logger.error(`Error getting block ${blockNumber}:`, error);
      throw error;
    }
  }

  async getLargeTransactions(
    minValueEth: number = 100,
    startBlock?: number,
    endBlock?: number
  ): Promise<EtherscanTransaction[]> {
    try {
      const latestBlock = await this.getLatestBlock();
      const fromBlock = startBlock || latestBlock - 1000; // Last ~1000 blocks
      const toBlock = endBlock || latestBlock;

      const minValueWei = (minValueEth * 1e18).toString();
      const largeTransactions: EtherscanTransaction[] = [];

      // This is a simplified approach - in production, you'd want to monitor
      // specific whale addresses or use more sophisticated methods
      logger.info(`Scanning blocks ${fromBlock} to ${toBlock} for large transactions (>${minValueEth} ETH)`);

      // For demo purposes, we'll just return empty array
      // In production, you'd implement block scanning or use specialized services
      return largeTransactions;
    } catch (error) {
      logger.error('Error getting large transactions:', error);
      throw error;
    }
  }

  async getTopEthereumAddresses(): Promise<string[]> {
    // This would typically come from a curated list or external service
    // These are some known whale/exchange addresses for demonstration
    return [
      '******************************************', // Binance 8
      '******************************************', // Binance 14
      '******************************************', // Binance 15
      '******************************************', // Binance 16
      '******************************************', // Binance 17
      '******************************************', // Binance 18
      '******************************************', // Binance 19
      '******************************************', // Binance 20
      '******************************************', // Binance 21
      '******************************************', // OKEx
    ];
  }

  // Convert Wei to Ether
  weiToEther(wei: string): number {
    return parseFloat(wei) / 1e18;
  }

  // Convert Ether to Wei
  etherToWei(ether: number): string {
    return (ether * 1e18).toString();
  }

  // Check if transaction value is considered "large"
  isLargeTransaction(valueWei: string, thresholdEth: number = 100): boolean {
    const valueEth = this.weiToEther(valueWei);
    return valueEth >= thresholdEth;
  }

  // Get USD value of ETH amount (you'd need to integrate with price service)
  async getUSDValue(ethAmount: number): Promise<number> {
    // This would integrate with your price service
    // For now, return a placeholder
    const ethPriceUSD = 2000; // Placeholder price
    return ethAmount * ethPriceUSD;
  }
}
