import { CoinGeckoService } from './CoinGeckoService';
import { PriceDataModel } from '../models/PriceDataModel';
import { CryptocurrencyModel } from '../models/CryptocurrencyModel';
import { PriceData, Cryptocurrency } from '../types';
import logger from '../config/logger';

export interface PriceUpdateResult {
  updated: number;
  failed: number;
  errors: string[];
}

export class PriceService {
  private coinGeckoService: CoinGeckoService;
  private priceDataModel: PriceDataModel;
  private cryptoModel: CryptocurrencyModel;
  private cache: Map<string, { data: any; timestamp: number }>;
  private cacheTimeout = 60000; // 1 minute cache

  constructor() {
    this.coinGeckoService = new CoinGeckoService();
    this.priceDataModel = new PriceDataModel();
    this.cryptoModel = new CryptocurrencyModel();
    this.cache = new Map();
  }

  private getCacheKey(method: string, params: any[]): string {
    return `${method}_${JSON.stringify(params)}`;
  }

  private getFromCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data;
    }
    return null;
  }

  private setCache(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  async getCurrentPrice(coinId: string): Promise<PriceData | null> {
    try {
      const cacheKey = this.getCacheKey('getCurrentPrice', [coinId]);
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }

      // Try to get from database first
      const latestPrice = await this.priceDataModel.getLatestPriceData(coinId);
      
      // If data is recent (less than 5 minutes old), return it
      if (latestPrice && Date.now() - latestPrice.timestamp.getTime() < 300000) {
        this.setCache(cacheKey, latestPrice);
        return latestPrice;
      }

      // Otherwise, fetch from API
      const crypto = await this.cryptoModel.findById(coinId);
      if (!crypto) {
        throw new Error(`Cryptocurrency not found: ${coinId}`);
      }

      const marketData = await this.coinGeckoService.getMarketData([crypto.coinGeckoId]);
      if (marketData.length === 0) {
        throw new Error(`No market data found for ${crypto.symbol}`);
      }

      const data = marketData[0];
      const priceData = await this.priceDataModel.createPriceData({
        coinId,
        price: data.current_price,
        priceChange24h: data.price_change_24h,
        priceChangePercentage24h: data.price_change_percentage_24h,
        marketCap: data.market_cap,
        volume24h: data.total_volume,
      });

      this.setCache(cacheKey, priceData);
      return priceData;
    } catch (error) {
      logger.error(`Error getting current price for ${coinId}:`, error);
      throw error;
    }
  }

  async updateAllPrices(): Promise<PriceUpdateResult> {
    try {
      const result: PriceUpdateResult = {
        updated: 0,
        failed: 0,
        errors: [],
      };

      // Get all active cryptocurrencies
      const cryptos = await this.cryptoModel.findAll(true);
      if (cryptos.length === 0) {
        logger.warn('No active cryptocurrencies found for price update');
        return result;
      }

      // Process in batches to avoid rate limits
      const batchSize = 50; // CoinGecko allows up to 250, but we'll be conservative
      const batches = [];
      
      for (let i = 0; i < cryptos.length; i += batchSize) {
        batches.push(cryptos.slice(i, i + batchSize));
      }

      for (const batch of batches) {
        try {
          const coinGeckoIds = batch.map(crypto => crypto.coinGeckoId);
          const marketData = await this.coinGeckoService.getMarketData(coinGeckoIds);

          const priceDataToCreate = [];

          for (const crypto of batch) {
            const data = marketData.find(item => item.id === crypto.coinGeckoId);
            if (data) {
              priceDataToCreate.push({
                coinId: crypto.id,
                price: data.current_price,
                priceChange24h: data.price_change_24h,
                priceChangePercentage24h: data.price_change_percentage_24h,
                marketCap: data.market_cap,
                volume24h: data.total_volume,
              });
            } else {
              result.failed++;
              result.errors.push(`No data found for ${crypto.symbol}`);
            }
          }

          if (priceDataToCreate.length > 0) {
            const created = await this.priceDataModel.bulkCreatePriceData(priceDataToCreate);
            result.updated += created;
          }

          // Clear cache for updated coins
          batch.forEach(crypto => {
            const cacheKey = this.getCacheKey('getCurrentPrice', [crypto.id]);
            this.cache.delete(cacheKey);
          });

        } catch (error) {
          logger.error('Error updating price batch:', error);
          result.failed += batch.length;
          result.errors.push(`Batch error: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      logger.info(`Price update completed: ${result.updated} updated, ${result.failed} failed`);
      return result;
    } catch (error) {
      logger.error('Error in updateAllPrices:', error);
      throw error;
    }
  }

  async getHistoricalPrices(
    coinId: string,
    days: number = 30
  ): Promise<{ timestamp: Date; price: number; volume: number; marketCap: number }[]> {
    try {
      const cacheKey = this.getCacheKey('getHistoricalPrices', [coinId, days]);
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }

      const crypto = await this.cryptoModel.findById(coinId);
      if (!crypto) {
        throw new Error(`Cryptocurrency not found: ${coinId}`);
      }

      const historicalData = await this.coinGeckoService.getHistoricalData(
        crypto.coinGeckoId,
        'usd',
        days
      );

      const prices = historicalData.prices.map((item, index) => ({
        timestamp: new Date(item[0]),
        price: item[1],
        volume: historicalData.total_volumes[index]?.[1] || 0,
        marketCap: historicalData.market_caps[index]?.[1] || 0,
      }));

      // Store in database for future reference
      const historicalPriceData = prices.map(item => ({
        coinId,
        price: item.price,
        volume: item.volume,
        marketCap: item.marketCap,
        timestamp: item.timestamp,
      }));

      await this.priceDataModel.bulkCreateHistoricalPrices(historicalPriceData);

      this.setCache(cacheKey, prices);
      return prices;
    } catch (error) {
      logger.error(`Error getting historical prices for ${coinId}:`, error);
      throw error;
    }
  }

  async searchCryptocurrencies(query: string): Promise<Cryptocurrency[]> {
    try {
      const cacheKey = this.getCacheKey('searchCryptocurrencies', [query]);
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }

      // First search in our database
      const localResults = await this.cryptoModel.search(query);
      
      // If we have good local results, return them
      if (localResults.length >= 5) {
        this.setCache(cacheKey, localResults);
        return localResults;
      }

      // Otherwise, search CoinGecko and potentially add new coins
      const coinGeckoResults = await this.coinGeckoService.searchCoins(query);
      
      // For now, just return local results
      // In a production system, you might want to add new coins from CoinGecko
      this.setCache(cacheKey, localResults);
      return localResults;
    } catch (error) {
      logger.error(`Error searching cryptocurrencies with query ${query}:`, error);
      throw error;
    }
  }

  async getTopCryptocurrencies(limit: number = 50): Promise<PriceData[]> {
    try {
      const cacheKey = this.getCacheKey('getTopCryptocurrencies', [limit]);
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        return cached;
      }

      const topCoins = await this.coinGeckoService.getTopCoins(limit);
      const results: PriceData[] = [];

      for (const coinData of topCoins) {
        // Try to find the coin in our database
        const crypto = await this.cryptoModel.findByCoinGeckoId(coinData.id);
        
        if (crypto) {
          // Create price data entry
          const priceData = await this.priceDataModel.createPriceData({
            coinId: crypto.id,
            price: coinData.current_price,
            priceChange24h: coinData.price_change_24h,
            priceChangePercentage24h: coinData.price_change_percentage_24h,
            marketCap: coinData.market_cap,
            volume24h: coinData.total_volume,
          });
          
          results.push(priceData);
        }
      }

      this.setCache(cacheKey, results);
      return results;
    } catch (error) {
      logger.error('Error getting top cryptocurrencies:', error);
      throw error;
    }
  }

  async getPriceChangeStats(coinId: string, hours: number = 24): Promise<{
    startPrice: number;
    endPrice: number;
    change: number;
    changePercentage: number;
  } | null> {
    try {
      return await this.priceDataModel.getPriceChangeStats(coinId, hours);
    } catch (error) {
      logger.error(`Error getting price change stats for ${coinId}:`, error);
      throw error;
    }
  }

  clearCache(): void {
    this.cache.clear();
    logger.info('Price service cache cleared');
  }

  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys()),
    };
  }
}
