import { 
  TechnicalSignal, 
  ChainAlert, 
  PriceData, 
  SignalType, 
  SignalStrength,
  ChainAlertType,
  AlertSeverity 
} from '../types';

export interface MessageTemplate {
  title: string;
  content: string;
  emoji: string;
  priority: 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT';
}

export interface PriceUpdateData {
  symbol: string;
  currentPrice: number;
  priceChange24h: number;
  priceChangePercentage24h: number;
  marketCap: number;
  volume24h: number;
  timestamp: Date;
}

export interface MarketSummaryData {
  totalMarketCap: number;
  totalVolume24h: number;
  btcDominance: number;
  topGainers: Array<{ symbol: string; change: number }>;
  topLosers: Array<{ symbol: string; change: number }>;
}

export class MessageTemplateService {
  
  // Technical Signal Templates
  createTechnicalSignalMessage(signal: TechnicalSignal, coinSymbol: string): MessageTemplate {
    const signalEmoji = this.getSignalEmoji(signal.signalType);
    const strengthEmoji = this.getStrengthEmoji(signal.strength);
    const isPulse = signal.isPulseSignal;
    
    const title = `${signalEmoji} ${coinSymbol} 技术信号${isPulse ? ' 🔥' : ''}`;
    
    const content = `
${signalEmoji} **${coinSymbol} 技术分析信号** ${strengthEmoji}

📊 **信号详情**
• 类型: ${this.getSignalTypeText(signal.signalType)}
• 强度: ${this.getStrengthText(signal.strength)}
${isPulse ? '• 🔥 **MACD脉冲信号** - 强烈关注!' : ''}

📈 **技术指标**
• MACD: ${signal.macdValue.toFixed(4)}
• 信号线: ${signal.macdSignal.toFixed(4)}
• 柱状图: ${signal.macdHistogram.toFixed(4)}
• RSI: ${signal.rsiValue.toFixed(2)}

💡 **分析说明**
${signal.description}

⏰ ${this.formatDateTime(signal.createdAt)}

${this.getSignalActionAdvice(signal.signalType)}
`;

    return {
      title,
      content,
      emoji: signalEmoji,
      priority: this.getSignalPriority(signal.strength, isPulse),
    };
  }

  // Price Alert Templates
  createPriceAlertMessage(data: PriceUpdateData, threshold: number): MessageTemplate {
    const isPositive = data.priceChangePercentage24h > 0;
    const emoji = isPositive ? '📈' : '📉';
    const changeEmoji = isPositive ? '🟢' : '🔴';
    
    const title = `${emoji} ${data.symbol} 价格提醒`;
    
    const content = `
${emoji} **${data.symbol} 价格变动提醒**

💰 **当前价格**: $${this.formatNumber(data.currentPrice)}
${changeEmoji} **24h变化**: ${this.formatPercentage(data.priceChangePercentage24h)} (${this.formatCurrency(data.priceChange24h)})

📊 **市场数据**
• 市值: ${this.formatLargeNumber(data.marketCap)}
• 24h交易量: ${this.formatLargeNumber(data.volume24h)}

🎯 **触发条件**: 价格变化超过 ${threshold}%

⏰ ${this.formatDateTime(data.timestamp)}

${this.getPriceActionAdvice(data.priceChangePercentage24h)}
`;

    return {
      title,
      content,
      emoji,
      priority: Math.abs(data.priceChangePercentage24h) > 10 ? 'HIGH' : 'NORMAL',
    };
  }

  // Chain Alert Templates
  createChainAlertMessage(alert: ChainAlert): MessageTemplate {
    const alertEmoji = this.getChainAlertEmoji(alert.alertType);
    const severityEmoji = this.getSeverityEmoji(alert.severity);
    
    const title = `${alertEmoji} 链上活动提醒`;
    
    const content = `
${alertEmoji} **链上活动监控** ${severityEmoji}

⛓️ **区块链**: ${this.getBlockchainText(alert.blockchain)}
🚨 **活动类型**: ${this.getChainAlertTypeText(alert.alertType)}
📊 **严重程度**: ${this.getSeverityText(alert.severity)}

📝 **详细描述**
${alert.description}

${this.formatChainAlertData(alert.data)}

⏰ ${this.formatDateTime(alert.createdAt)}

${this.getChainAlertAdvice(alert.alertType)}
`;

    return {
      title,
      content,
      emoji: alertEmoji,
      priority: this.getChainAlertPriority(alert.severity),
    };
  }

  // Market Summary Templates
  createMarketSummaryMessage(data: MarketSummaryData): MessageTemplate {
    const title = '📊 市场概览';
    
    const content = `
📊 **加密货币市场概览**

🌍 **全球市场**
• 总市值: ${this.formatLargeNumber(data.totalMarketCap)}
• 24h总交易量: ${this.formatLargeNumber(data.totalVolume24h)}
• BTC市值占比: ${this.formatPercentage(data.btcDominance)}

📈 **今日涨幅榜**
${data.topGainers.map((coin, index) => 
  `${index + 1}. ${coin.symbol}: ${this.formatPercentage(coin.change)}`
).join('\n')}

📉 **今日跌幅榜**
${data.topLosers.map((coin, index) => 
  `${index + 1}. ${coin.symbol}: ${this.formatPercentage(coin.change)}`
).join('\n')}

⏰ ${this.formatDateTime(new Date())}

💡 使用 /price <币种> 查看详细信息
`;

    return {
      title,
      content,
      emoji: '📊',
      priority: 'NORMAL',
    };
  }

  // Welcome Message Template
  createWelcomeMessage(userName: string): MessageTemplate {
    const title = '🎉 欢迎使用加密货币助手';
    
    const content = `
🎉 **欢迎, ${userName}!**

感谢您使用我们的加密货币交易辅助机器人！

🚀 **主要功能**
• 📊 实时技术指标分析
• 💰 价格变动提醒
• ⛓️ 链上数据监控
• 🔔 智能推送通知

💡 **快速开始**
• /subscribe - 订阅币种监控
• /price BTC - 查看比特币价格
• /settings - 个人设置
• /help - 查看帮助

🎯 **专业分析**
我们的机器人使用MACD和RSI技术指标，结合链上数据分析，为您提供专业的投资参考。

⚠️ **风险提示**
投资有风险，请谨慎决策。本机器人提供的信息仅供参考，不构成投资建议。

开始您的加密货币投资之旅吧！🌟
`;

    return {
      title,
      content,
      emoji: '🎉',
      priority: 'NORMAL',
    };
  }

  // Daily Summary Template
  createDailySummaryMessage(userSubscriptions: string[], signalCount: number, alertCount: number): MessageTemplate {
    const title = '📅 每日总结';
    
    const content = `
📅 **每日市场总结**

👀 **您的关注**
关注币种: ${userSubscriptions.join(', ')}

📊 **今日活动**
• 技术信号: ${signalCount} 个
• 链上提醒: ${alertCount} 个

💡 **建议操作**
• 查看最新信号: /signals
• 检查价格变化: /price
• 调整设置: /settings

⏰ ${this.formatDateTime(new Date())}

祝您投资顺利！🍀
`;

    return {
      title,
      content,
      emoji: '📅',
      priority: 'NORMAL',
    };
  }

  // Helper Methods
  private getSignalEmoji(signalType: SignalType): string {
    const emojiMap: Record<SignalType, string> = {
      [SignalType.BUY]: '🟢',
      [SignalType.STRONG_BUY]: '🟢',
      [SignalType.SELL]: '🔴',
      [SignalType.STRONG_SELL]: '🔴',
      [SignalType.HOLD]: '🟡',
    };
    return emojiMap[signalType] || '⚪';
  }

  private getStrengthEmoji(strength: SignalStrength): string {
    const emojiMap: Record<SignalStrength, string> = {
      [SignalStrength.VERY_STRONG]: '🔥🔥🔥',
      [SignalStrength.STRONG]: '🔥🔥',
      [SignalStrength.MODERATE]: '🔥',
      [SignalStrength.WEAK]: '💨',
    };
    return emojiMap[strength] || '';
  }

  private getChainAlertEmoji(alertType: ChainAlertType): string {
    const emojiMap: Record<ChainAlertType, string> = {
      [ChainAlertType.LARGE_TRANSACTION]: '💰',
      [ChainAlertType.WHALE_MOVEMENT]: '🐋',
      [ChainAlertType.EXCHANGE_INFLOW]: '📥',
      [ChainAlertType.EXCHANGE_OUTFLOW]: '📤',
      [ChainAlertType.UNUSUAL_VOLUME]: '📊',
    };
    return emojiMap[alertType] || '🚨';
  }

  private getSeverityEmoji(severity: AlertSeverity): string {
    const emojiMap: Record<AlertSeverity, string> = {
      [AlertSeverity.CRITICAL]: '🚨🚨🚨',
      [AlertSeverity.HIGH]: '🚨🚨',
      [AlertSeverity.MEDIUM]: '🚨',
      [AlertSeverity.LOW]: '💡',
    };
    return emojiMap[severity] || '';
  }

  private getSignalTypeText(signalType: SignalType): string {
    const textMap: Record<SignalType, string> = {
      [SignalType.BUY]: '买入信号',
      [SignalType.STRONG_BUY]: '强烈买入',
      [SignalType.SELL]: '卖出信号',
      [SignalType.STRONG_SELL]: '强烈卖出',
      [SignalType.HOLD]: '持有观望',
    };
    return textMap[signalType] || signalType;
  }

  private getStrengthText(strength: SignalStrength): string {
    const textMap: Record<SignalStrength, string> = {
      [SignalStrength.VERY_STRONG]: '非常强',
      [SignalStrength.STRONG]: '强',
      [SignalStrength.MODERATE]: '中等',
      [SignalStrength.WEAK]: '弱',
    };
    return textMap[strength] || strength;
  }

  private getChainAlertTypeText(alertType: ChainAlertType): string {
    const textMap: Record<ChainAlertType, string> = {
      [ChainAlertType.LARGE_TRANSACTION]: '大额转账',
      [ChainAlertType.WHALE_MOVEMENT]: '巨鲸活动',
      [ChainAlertType.EXCHANGE_INFLOW]: '交易所流入',
      [ChainAlertType.EXCHANGE_OUTFLOW]: '交易所流出',
      [ChainAlertType.UNUSUAL_VOLUME]: '异常交易量',
    };
    return textMap[alertType] || alertType;
  }

  private getSeverityText(severity: AlertSeverity): string {
    const textMap: Record<AlertSeverity, string> = {
      [AlertSeverity.CRITICAL]: '严重',
      [AlertSeverity.HIGH]: '高',
      [AlertSeverity.MEDIUM]: '中等',
      [AlertSeverity.LOW]: '低',
    };
    return textMap[severity] || severity;
  }

  private getBlockchainText(blockchain: string): string {
    const textMap: Record<string, string> = {
      'ETHEREUM': '以太坊',
      'BSC': '币安智能链',
      'POLYGON': 'Polygon',
      'ARBITRUM': 'Arbitrum',
      'OPTIMISM': 'Optimism',
    };
    return textMap[blockchain] || blockchain;
  }

  private getSignalPriority(strength: SignalStrength, isPulse: boolean): 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT' {
    if (isPulse) return 'HIGH';
    if (strength === SignalStrength.VERY_STRONG) return 'HIGH';
    if (strength === SignalStrength.STRONG) return 'NORMAL';
    return 'LOW';
  }

  private getChainAlertPriority(severity: AlertSeverity): 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT' {
    const priorityMap: Record<AlertSeverity, 'LOW' | 'NORMAL' | 'HIGH' | 'URGENT'> = {
      [AlertSeverity.CRITICAL]: 'URGENT',
      [AlertSeverity.HIGH]: 'HIGH',
      [AlertSeverity.MEDIUM]: 'NORMAL',
      [AlertSeverity.LOW]: 'LOW',
    };
    return priorityMap[severity] || 'NORMAL';
  }

  private formatDateTime(date: Date): string {
    return date.toLocaleString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
    });
  }

  private formatNumber(num: number): string {
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 6,
    });
  }

  private formatCurrency(num: number): string {
    const sign = num >= 0 ? '+' : '';
    return `${sign}$${this.formatNumber(Math.abs(num))}`;
  }

  private formatPercentage(num: number): string {
    const sign = num >= 0 ? '+' : '';
    return `${sign}${num.toFixed(2)}%`;
  }

  private formatLargeNumber(num: number): string {
    if (num >= 1e12) {
      return `$${(num / 1e12).toFixed(2)}T`;
    } else if (num >= 1e9) {
      return `$${(num / 1e9).toFixed(2)}B`;
    } else if (num >= 1e6) {
      return `$${(num / 1e6).toFixed(2)}M`;
    } else if (num >= 1e3) {
      return `$${(num / 1e3).toFixed(2)}K`;
    }
    return `$${this.formatNumber(num)}`;
  }

  private formatChainAlertData(data: any): string {
    if (!data) return '';
    
    let formatted = '📋 **详细数据**\n';
    
    if (data.hash) {
      formatted += `• 交易哈希: \`${data.hash.substring(0, 10)}...\`\n`;
    }
    
    if (data.valueUSD) {
      formatted += `• 金额: ${this.formatLargeNumber(data.valueUSD)}\n`;
    }
    
    if (data.from && data.to) {
      formatted += `• 从: \`${data.from.substring(0, 10)}...\`\n`;
      formatted += `• 到: \`${data.to.substring(0, 10)}...\`\n`;
    }
    
    return formatted;
  }

  private getSignalActionAdvice(signalType: SignalType): string {
    const adviceMap: Record<SignalType, string> = {
      [SignalType.BUY]: '💡 **建议**: 考虑逢低买入，注意风险控制',
      [SignalType.STRONG_BUY]: '💡 **建议**: 强烈买入信号，但请谨慎投资',
      [SignalType.SELL]: '💡 **建议**: 考虑获利了结，保护收益',
      [SignalType.STRONG_SELL]: '💡 **建议**: 强烈卖出信号，及时止损',
      [SignalType.HOLD]: '💡 **建议**: 保持观望，等待更明确信号',
    };
    return adviceMap[signalType] || '';
  }

  private getPriceActionAdvice(changePercentage: number): string {
    if (changePercentage > 10) {
      return '💡 **建议**: 大幅上涨，注意获利了结风险';
    } else if (changePercentage > 5) {
      return '💡 **建议**: 上涨趋势，可考虑部分获利';
    } else if (changePercentage < -10) {
      return '💡 **建议**: 大幅下跌，可考虑逢低买入';
    } else if (changePercentage < -5) {
      return '💡 **建议**: 下跌趋势，注意风险控制';
    }
    return '💡 **建议**: 价格波动正常，保持观察';
  }

  private getChainAlertAdvice(alertType: ChainAlertType): string {
    const adviceMap: Record<ChainAlertType, string> = {
      [ChainAlertType.LARGE_TRANSACTION]: '💡 **关注**: 大额转账可能影响市场流动性',
      [ChainAlertType.WHALE_MOVEMENT]: '💡 **关注**: 巨鲸活动可能预示价格变动',
      [ChainAlertType.EXCHANGE_INFLOW]: '💡 **关注**: 资金流入交易所，可能有抛售压力',
      [ChainAlertType.EXCHANGE_OUTFLOW]: '💡 **关注**: 资金流出交易所，可能减少抛售压力',
      [ChainAlertType.UNUSUAL_VOLUME]: '💡 **关注**: 异常交易量，市场可能有重大变化',
    };
    return adviceMap[alertType] || '';
  }
}
