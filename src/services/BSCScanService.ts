import axios, { AxiosInstance } from 'axios';
import config from '../config';
import logger from '../config/logger';
import { EtherscanTransaction } from '../types';

export class BSCScanService {
  private client: AxiosInstance;
  private apiKey: string;
  private baseURL = 'https://api.bscscan.com/api';
  private rateLimitDelay = 200; // 5 requests per second

  constructor() {
    this.apiKey = config.apiKeys.bscScan;
    
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      params: {
        apikey: this.apiKey,
      },
    });

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    this.client.interceptors.request.use(async (config) => {
      await this.delay(this.rateLimitDelay);
      return config;
    });

    this.client.interceptors.response.use(
      (response) => {
        if (response.data.status === '0' && response.data.message !== 'No transactions found') {
          throw new Error(response.data.result || 'BSCScan API error');
        }
        return response;
      },
      (error) => {
        logger.error('BSCScan API error:', error.message);
        throw error;
      }
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getAccountBalance(address: string): Promise<string> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'account',
          action: 'balance',
          address,
          tag: 'latest',
        },
      });

      return response.data.result;
    } catch (error) {
      logger.error(`Error getting BSC balance for ${address}:`, error);
      throw error;
    }
  }

  async getTransactions(
    address: string,
    startBlock = 0,
    endBlock = ********,
    page = 1,
    offset = 100
  ): Promise<EtherscanTransaction[]> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'account',
          action: 'txlist',
          address,
          startblock: startBlock,
          endblock: endBlock,
          page,
          offset: Math.min(offset, 10000),
          sort: 'desc',
        },
      });

      return response.data.result || [];
    } catch (error) {
      logger.error(`Error getting BSC transactions for ${address}:`, error);
      throw error;
    }
  }

  async getBEP20TokenTransfers(
    contractAddress?: string,
    address?: string,
    startBlock = 0,
    endBlock = ********,
    page = 1,
    offset = 100
  ): Promise<any[]> {
    try {
      const params: any = {
        module: 'account',
        action: 'tokentx',
        startblock: startBlock,
        endblock: endBlock,
        page,
        offset: Math.min(offset, 10000),
        sort: 'desc',
      };

      if (contractAddress) {
        params.contractaddress = contractAddress;
      }

      if (address) {
        params.address = address;
      }

      const response = await this.client.get('', { params });
      return response.data.result || [];
    } catch (error) {
      logger.error('Error getting BEP20 token transfers:', error);
      throw error;
    }
  }

  async getLatestBlock(): Promise<number> {
    try {
      const response = await this.client.get('', {
        params: {
          module: 'proxy',
          action: 'eth_blockNumber',
        },
      });

      return parseInt(response.data.result, 16);
    } catch (error) {
      logger.error('Error getting latest BSC block:', error);
      throw error;
    }
  }

  async getTopBSCAddresses(): Promise<string[]> {
    // Known BSC whale/exchange addresses
    return [
      '******************************************', // Binance Hot Wallet
      '******************************************', // Binance Hot Wallet 2
      '******************************************', // Binance Hot Wallet 3
      '******************************************', // Binance Hot Wallet 4
      '******************************************', // Binance Hot Wallet 5
      '******************************************', // Binance Hot Wallet 6
      '******************************************', // PancakeSwap
      '******************************************', // PancakeSwap V2
      '******************************************', // PancakeSwap V2 Router
      '******************************************', // PancakeSwap V2 Router 2
    ];
  }

  weiToEther(wei: string): number {
    return parseFloat(wei) / 1e18;
  }

  etherToWei(ether: number): string {
    return (ether * 1e18).toString();
  }

  isLargeTransaction(valueWei: string, thresholdBNB: number = 50): boolean {
    const valueBNB = this.weiToEther(valueWei);
    return valueBNB >= thresholdBNB;
  }
}
