import prisma from '../config/database';
import { User } from '../types';
import logger from '../config/logger';

export interface CreateUserData {
  telegramId: number;
  username?: string;
  firstName?: string;
  lastName?: string;
}

export interface UpdateUserData {
  username?: string;
  firstName?: string;
  lastName?: string;
  isActive?: boolean;
  isPremium?: boolean;
}

export class UserService {
  async getUserByTelegramId(telegramId: number): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { telegramId: BigInt(telegramId) },
      });

      if (!user) return null;

      return {
        ...user,
        telegramId: Number(user.telegramId),
      };
    } catch (error) {
      logger.error('Error getting user by telegram ID:', error);
      throw error;
    }
  }

  async getUserById(id: string): Promise<User | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id },
      });

      if (!user) return null;

      return {
        ...user,
        telegramId: Number(user.telegramId),
      };
    } catch (error) {
      logger.error('Error getting user by ID:', error);
      throw error;
    }
  }

  async createUser(data: CreateUserData): Promise<User> {
    try {
      const user = await prisma.user.create({
        data: {
          telegramId: BigInt(data.telegramId),
          username: data.username,
          firstName: data.firstName,
          lastName: data.lastName,
        },
      });

      logger.info(`User created: ${user.id} (${data.telegramId})`);

      return {
        ...user,
        telegramId: Number(user.telegramId),
      };
    } catch (error) {
      logger.error('Error creating user:', error);
      throw error;
    }
  }

  async updateUser(id: string, data: UpdateUserData): Promise<User> {
    try {
      const user = await prisma.user.update({
        where: { id },
        data,
      });

      logger.info(`User updated: ${user.id}`);

      return {
        ...user,
        telegramId: Number(user.telegramId),
      };
    } catch (error) {
      logger.error('Error updating user:', error);
      throw error;
    }
  }

  async deleteUser(id: string): Promise<void> {
    try {
      await prisma.user.delete({
        where: { id },
      });

      logger.info(`User deleted: ${id}`);
    } catch (error) {
      logger.error('Error deleting user:', error);
      throw error;
    }
  }

  async getAllUsers(): Promise<User[]> {
    try {
      const users = await prisma.user.findMany({
        where: { isActive: true },
        orderBy: { createdAt: 'desc' },
      });

      return users.map(user => ({
        ...user,
        telegramId: Number(user.telegramId),
      }));
    } catch (error) {
      logger.error('Error getting all users:', error);
      throw error;
    }
  }

  async getUserCount(): Promise<number> {
    try {
      return await prisma.user.count({
        where: { isActive: true },
      });
    } catch (error) {
      logger.error('Error getting user count:', error);
      throw error;
    }
  }

  async getPremiumUsers(): Promise<User[]> {
    try {
      const users = await prisma.user.findMany({
        where: { 
          isActive: true,
          isPremium: true,
        },
        orderBy: { createdAt: 'desc' },
      });

      return users.map(user => ({
        ...user,
        telegramId: Number(user.telegramId),
      }));
    } catch (error) {
      logger.error('Error getting premium users:', error);
      throw error;
    }
  }

  async deactivateUser(id: string): Promise<User> {
    try {
      const user = await prisma.user.update({
        where: { id },
        data: { isActive: false },
      });

      logger.info(`User deactivated: ${user.id}`);

      return {
        ...user,
        telegramId: Number(user.telegramId),
      };
    } catch (error) {
      logger.error('Error deactivating user:', error);
      throw error;
    }
  }

  async activateUser(id: string): Promise<User> {
    try {
      const user = await prisma.user.update({
        where: { id },
        data: { isActive: true },
      });

      logger.info(`User activated: ${user.id}`);

      return {
        ...user,
        telegramId: Number(user.telegramId),
      };
    } catch (error) {
      logger.error('Error activating user:', error);
      throw error;
    }
  }
}
