import { UserSubscriptionModel } from '../models/UserSubscriptionModel';
import { CryptocurrencyModel } from '../models/CryptocurrencyModel';
import { UserService } from './UserService';
import { UserSubscription, PushFrequency } from '../types';
import logger from '../config/logger';

export interface CreateSubscriptionData {
  userId: string;
  coinSymbol: string;
  enablePriceAlerts?: boolean;
  enableTechnicalSignals?: boolean;
  enableChainAlerts?: boolean;
  pushFrequency?: PushFrequency;
  priceChangeThreshold?: number;
  volumeChangeThreshold?: number;
}

export interface UpdateSubscriptionData {
  enablePriceAlerts?: boolean;
  enableTechnicalSignals?: boolean;
  enableChainAlerts?: boolean;
  pushFrequency?: PushFrequency;
  priceChangeThreshold?: number;
  volumeChangeThreshold?: number;
}

export interface SubscriptionStats {
  totalSubscriptions: number;
  activeSubscriptions: number;
  subscriptionsByFrequency: Record<PushFrequency, number>;
  topCoins: Array<{ symbol: string; count: number }>;
}

export class SubscriptionService {
  private subscriptionModel: UserSubscriptionModel;
  private cryptoModel: CryptocurrencyModel;
  private userService: UserService;

  constructor() {
    this.subscriptionModel = new UserSubscriptionModel();
    this.cryptoModel = new CryptocurrencyModel();
    this.userService = new UserService();
  }

  async createSubscription(data: CreateSubscriptionData): Promise<UserSubscription> {
    try {
      // Validate user exists
      const user = await this.userService.getUserById(data.userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Find cryptocurrency by symbol
      const crypto = await this.cryptoModel.findBySymbol(data.coinSymbol.toUpperCase());
      if (!crypto) {
        throw new Error(`Cryptocurrency not found: ${data.coinSymbol}`);
      }

      // Check if subscription already exists
      const existingSubscription = await this.subscriptionModel.findByUserAndCoin(data.userId, crypto.id);
      if (existingSubscription) {
        if (existingSubscription.isActive) {
          throw new Error(`You are already subscribed to ${crypto.symbol}`);
        } else {
          // Reactivate existing subscription
          return await this.subscriptionModel.activate(existingSubscription.id);
        }
      }

      // Check subscription limits for non-premium users
      if (!user.isPremium) {
        const userSubscriptionCount = await this.subscriptionModel.getCountByUserId(data.userId);
        if (userSubscriptionCount >= 5) {
          throw new Error('Free users can only subscribe to 5 cryptocurrencies. Upgrade to premium for unlimited subscriptions.');
        }
      }

      // Create new subscription
      const subscription = await this.subscriptionModel.create({
        userId: data.userId,
        coinId: crypto.id,
        enablePriceAlerts: data.enablePriceAlerts ?? true,
        enableTechnicalSignals: data.enableTechnicalSignals ?? true,
        enableChainAlerts: data.enableChainAlerts ?? true,
        pushFrequency: data.pushFrequency ?? PushFrequency.DAILY,
        priceChangeThreshold: data.priceChangeThreshold ?? 5.0,
        volumeChangeThreshold: data.volumeChangeThreshold ?? 20.0,
      });

      logger.info(`User ${data.userId} subscribed to ${crypto.symbol}`);
      return subscription;

    } catch (error) {
      logger.error('Error creating subscription:', error);
      throw error;
    }
  }

  async updateSubscription(subscriptionId: string, data: UpdateSubscriptionData): Promise<UserSubscription> {
    try {
      const subscription = await this.subscriptionModel.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const updatedSubscription = await this.subscriptionModel.update(subscriptionId, data);
      
      logger.info(`Subscription ${subscriptionId} updated`);
      return updatedSubscription;

    } catch (error) {
      logger.error('Error updating subscription:', error);
      throw error;
    }
  }

  async deleteSubscription(subscriptionId: string): Promise<void> {
    try {
      const subscription = await this.subscriptionModel.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      await this.subscriptionModel.delete(subscriptionId);
      
      logger.info(`Subscription ${subscriptionId} deleted`);

    } catch (error) {
      logger.error('Error deleting subscription:', error);
      throw error;
    }
  }

  async deactivateSubscription(subscriptionId: string): Promise<UserSubscription> {
    try {
      const subscription = await this.subscriptionModel.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const deactivatedSubscription = await this.subscriptionModel.deactivate(subscriptionId);
      
      logger.info(`Subscription ${subscriptionId} deactivated`);
      return deactivatedSubscription;

    } catch (error) {
      logger.error('Error deactivating subscription:', error);
      throw error;
    }
  }

  async activateSubscription(subscriptionId: string): Promise<UserSubscription> {
    try {
      const subscription = await this.subscriptionModel.findById(subscriptionId);
      if (!subscription) {
        throw new Error('Subscription not found');
      }

      const activatedSubscription = await this.subscriptionModel.activate(subscriptionId);
      
      logger.info(`Subscription ${subscriptionId} activated`);
      return activatedSubscription;

    } catch (error) {
      logger.error('Error activating subscription:', error);
      throw error;
    }
  }

  async getUserSubscriptions(userId: string, activeOnly = true): Promise<UserSubscription[]> {
    try {
      return await this.subscriptionModel.findByUserId(userId, activeOnly);
    } catch (error) {
      logger.error('Error getting user subscriptions:', error);
      throw error;
    }
  }

  async getCoinSubscriptions(coinId: string, activeOnly = true): Promise<UserSubscription[]> {
    try {
      return await this.subscriptionModel.findByCoinId(coinId, activeOnly);
    } catch (error) {
      logger.error('Error getting coin subscriptions:', error);
      throw error;
    }
  }

  async getSubscriptionsByFrequency(frequency: PushFrequency): Promise<UserSubscription[]> {
    try {
      return await this.subscriptionModel.findByPushFrequency(frequency, true);
    } catch (error) {
      logger.error('Error getting subscriptions by frequency:', error);
      throw error;
    }
  }

  async getAllActiveSubscriptions(): Promise<UserSubscription[]> {
    try {
      return await this.subscriptionModel.findActiveSubscriptions();
    } catch (error) {
      logger.error('Error getting all active subscriptions:', error);
      throw error;
    }
  }

  async getSubscriptionStats(): Promise<SubscriptionStats> {
    try {
      const [totalSubscriptions, activeSubscriptions, allSubscriptions] = await Promise.all([
        this.subscriptionModel.getTotalActiveCount(),
        this.subscriptionModel.getTotalActiveCount(),
        this.subscriptionModel.findActiveSubscriptions(),
      ]);

      // Count subscriptions by frequency
      const subscriptionsByFrequency = Object.values(PushFrequency).reduce((acc, frequency) => {
        acc[frequency] = allSubscriptions.filter(sub => sub.pushFrequency === frequency).length;
        return acc;
      }, {} as Record<PushFrequency, number>);

      // Count top coins
      const coinCounts = allSubscriptions.reduce((acc, sub) => {
        const symbol = sub.coin.symbol;
        acc[symbol] = (acc[symbol] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      const topCoins = Object.entries(coinCounts)
        .map(([symbol, count]) => ({ symbol, count }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 10);

      return {
        totalSubscriptions,
        activeSubscriptions,
        subscriptionsByFrequency,
        topCoins,
      };

    } catch (error) {
      logger.error('Error getting subscription stats:', error);
      throw error;
    }
  }

  async canUserSubscribe(userId: string): Promise<{
    canSubscribe: boolean;
    reason?: string;
    currentCount: number;
    maxAllowed: number;
  }> {
    try {
      const user = await this.userService.getUserById(userId);
      if (!user) {
        return {
          canSubscribe: false,
          reason: 'User not found',
          currentCount: 0,
          maxAllowed: 0,
        };
      }

      const currentCount = await this.subscriptionModel.getCountByUserId(userId);
      const maxAllowed = user.isPremium ? 100 : 5; // Premium users get 100, free users get 5

      const canSubscribe = currentCount < maxAllowed;

      return {
        canSubscribe,
        reason: canSubscribe ? undefined : `Maximum subscriptions reached (${maxAllowed})`,
        currentCount,
        maxAllowed,
      };

    } catch (error) {
      logger.error('Error checking if user can subscribe:', error);
      throw error;
    }
  }

  async bulkUpdateSubscriptions(
    userId: string,
    updates: { subscriptionId: string; data: UpdateSubscriptionData }[]
  ): Promise<{
    updated: number;
    errors: string[];
  }> {
    try {
      const result = {
        updated: 0,
        errors: [],
      };

      for (const update of updates) {
        try {
          const subscription = await this.subscriptionModel.findById(update.subscriptionId);
          
          if (!subscription) {
            result.errors.push(`Subscription ${update.subscriptionId} not found`);
            continue;
          }

          if (subscription.userId !== userId) {
            result.errors.push(`Subscription ${update.subscriptionId} does not belong to user`);
            continue;
          }

          await this.subscriptionModel.update(update.subscriptionId, update.data);
          result.updated++;

        } catch (error) {
          const errorMsg = `Failed to update subscription ${update.subscriptionId}: ${error instanceof Error ? error.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
        }
      }

      logger.info(`Bulk update completed for user ${userId}: ${result.updated} updated, ${result.errors.length} errors`);
      return result;

    } catch (error) {
      logger.error('Error in bulk update subscriptions:', error);
      throw error;
    }
  }

  async getSubscriptionById(subscriptionId: string): Promise<UserSubscription | null> {
    try {
      return await this.subscriptionModel.findById(subscriptionId);
    } catch (error) {
      logger.error('Error getting subscription by ID:', error);
      throw error;
    }
  }

  async searchUserSubscriptions(userId: string, query: string): Promise<UserSubscription[]> {
    try {
      const userSubscriptions = await this.getUserSubscriptions(userId, true);
      
      const filteredSubscriptions = userSubscriptions.filter(subscription => 
        subscription.coin.symbol.toLowerCase().includes(query.toLowerCase()) ||
        subscription.coin.name.toLowerCase().includes(query.toLowerCase())
      );

      return filteredSubscriptions;

    } catch (error) {
      logger.error('Error searching user subscriptions:', error);
      throw error;
    }
  }
}
