import axios, { AxiosInstance, AxiosResponse } from 'axios';
import config from '../config';
import logger from '../config/logger';
import { CoinGeckoPrice } from '../types';

export interface CoinGeckoMarketData {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number | null;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number | null;
  max_supply: number | null;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

export interface CoinGeckoCoin {
  id: string;
  symbol: string;
  name: string;
  platforms: Record<string, string>;
}

export interface CoinGeckoHistoricalData {
  prices: [number, number][];
  market_caps: [number, number][];
  total_volumes: [number, number][];
}

export class CoinGeckoService {
  private client: AxiosInstance;
  private baseURL = 'https://api.coingecko.com/api/v3';
  private rateLimitDelay = 1000; // 1 second between requests for free tier

  constructor() {
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: 30000,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'CryptoBot/1.0',
      },
    });

    // Add API key if available
    if (config.apiKeys.coinGecko) {
      this.client.defaults.headers.common['x-cg-pro-api-key'] = config.apiKeys.coinGecko;
      this.rateLimitDelay = 100; // Faster rate limit for pro users
    }

    this.setupInterceptors();
  }

  private setupInterceptors(): void {
    // Request interceptor for rate limiting
    this.client.interceptors.request.use(async (config) => {
      // Simple rate limiting
      await this.delay(this.rateLimitDelay);
      return config;
    });

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 429) {
          logger.warn('CoinGecko rate limit exceeded');
          throw new Error('Rate limit exceeded. Please try again later.');
        }
        
        if (error.response?.status === 404) {
          logger.warn('CoinGecko resource not found');
          throw new Error('Requested cryptocurrency not found.');
        }

        logger.error('CoinGecko API error:', error.message);
        throw error;
      }
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  async getMarketData(
    coinIds: string[],
    vsCurrency = 'usd',
    includeMarketCap = true,
    include24hrVol = true,
    include24hrChange = true
  ): Promise<CoinGeckoMarketData[]> {
    try {
      const params = {
        ids: coinIds.join(','),
        vs_currency: vsCurrency,
        include_market_cap: includeMarketCap,
        include_24hr_vol: include24hrVol,
        include_24hr_change: include24hrChange,
        include_last_updated_at: true,
      };

      const response: AxiosResponse<CoinGeckoMarketData[]> = await this.client.get(
        '/coins/markets',
        { params }
      );

      logger.debug(`Retrieved market data for ${response.data.length} coins`);
      return response.data;
    } catch (error) {
      logger.error('Error fetching market data from CoinGecko:', error);
      throw error;
    }
  }

  async getCoinById(coinId: string, includeMarketData = true): Promise<any> {
    try {
      const params = {
        localization: false,
        tickers: false,
        market_data: includeMarketData,
        community_data: false,
        developer_data: false,
        sparkline: false,
      };

      const response = await this.client.get(`/coins/${coinId}`, { params });
      
      logger.debug(`Retrieved detailed data for coin: ${coinId}`);
      return response.data;
    } catch (error) {
      logger.error(`Error fetching coin data for ${coinId}:`, error);
      throw error;
    }
  }

  async getHistoricalData(
    coinId: string,
    vsCurrency = 'usd',
    days = 30
  ): Promise<CoinGeckoHistoricalData> {
    try {
      const params = {
        vs_currency: vsCurrency,
        days: days.toString(),
        interval: days <= 1 ? 'hourly' : 'daily',
      };

      const response: AxiosResponse<CoinGeckoHistoricalData> = await this.client.get(
        `/coins/${coinId}/market_chart`,
        { params }
      );

      logger.debug(`Retrieved ${days} days of historical data for ${coinId}`);
      return response.data;
    } catch (error) {
      logger.error(`Error fetching historical data for ${coinId}:`, error);
      throw error;
    }
  }

  async searchCoins(query: string): Promise<CoinGeckoCoin[]> {
    try {
      const response = await this.client.get('/search', {
        params: { query },
      });

      const coins = response.data.coins || [];
      logger.debug(`Found ${coins.length} coins matching query: ${query}`);
      return coins;
    } catch (error) {
      logger.error(`Error searching coins with query ${query}:`, error);
      throw error;
    }
  }

  async getAllCoins(): Promise<CoinGeckoCoin[]> {
    try {
      const response: AxiosResponse<CoinGeckoCoin[]> = await this.client.get('/coins/list', {
        params: { include_platform: true },
      });

      logger.debug(`Retrieved ${response.data.length} total coins`);
      return response.data;
    } catch (error) {
      logger.error('Error fetching all coins:', error);
      throw error;
    }
  }

  async getTopCoins(limit = 100, vsCurrency = 'usd'): Promise<CoinGeckoMarketData[]> {
    try {
      const params = {
        vs_currency: vsCurrency,
        order: 'market_cap_desc',
        per_page: Math.min(limit, 250), // CoinGecko max is 250
        page: 1,
        sparkline: false,
        include_24hr_change: true,
      };

      const response: AxiosResponse<CoinGeckoMarketData[]> = await this.client.get(
        '/coins/markets',
        { params }
      );

      logger.debug(`Retrieved top ${response.data.length} coins by market cap`);
      return response.data;
    } catch (error) {
      logger.error('Error fetching top coins:', error);
      throw error;
    }
  }

  async getSimplePrice(
    coinIds: string[],
    vsCurrencies = ['usd'],
    includeMarketCap = false,
    include24hrVol = false,
    include24hrChange = true,
    includeLastUpdated = true
  ): Promise<Record<string, any>> {
    try {
      const params = {
        ids: coinIds.join(','),
        vs_currencies: vsCurrencies.join(','),
        include_market_cap: includeMarketCap,
        include_24hr_vol: include24hrVol,
        include_24hr_change: include24hrChange,
        include_last_updated_at: includeLastUpdated,
      };

      const response = await this.client.get('/simple/price', { params });
      
      logger.debug(`Retrieved simple price data for ${coinIds.length} coins`);
      return response.data;
    } catch (error) {
      logger.error('Error fetching simple price data:', error);
      throw error;
    }
  }

  async getTrendingCoins(): Promise<any> {
    try {
      const response = await this.client.get('/search/trending');
      
      logger.debug('Retrieved trending coins data');
      return response.data;
    } catch (error) {
      logger.error('Error fetching trending coins:', error);
      throw error;
    }
  }

  async getGlobalMarketData(): Promise<any> {
    try {
      const response = await this.client.get('/global');
      
      logger.debug('Retrieved global market data');
      return response.data;
    } catch (error) {
      logger.error('Error fetching global market data:', error);
      throw error;
    }
  }

  // Helper method to convert CoinGecko data to our internal format
  convertToInternalFormat(coinGeckoData: CoinGeckoMarketData): CoinGeckoPrice {
    return {
      id: coinGeckoData.id,
      symbol: coinGeckoData.symbol,
      name: coinGeckoData.name,
      current_price: coinGeckoData.current_price,
      market_cap: coinGeckoData.market_cap,
      market_cap_rank: coinGeckoData.market_cap_rank,
      fully_diluted_valuation: coinGeckoData.fully_diluted_valuation || 0,
      total_volume: coinGeckoData.total_volume,
      high_24h: coinGeckoData.high_24h,
      low_24h: coinGeckoData.low_24h,
      price_change_24h: coinGeckoData.price_change_24h,
      price_change_percentage_24h: coinGeckoData.price_change_percentage_24h,
      market_cap_change_24h: coinGeckoData.market_cap_change_24h,
      market_cap_change_percentage_24h: coinGeckoData.market_cap_change_percentage_24h,
      circulating_supply: coinGeckoData.circulating_supply,
      total_supply: coinGeckoData.total_supply || 0,
      max_supply: coinGeckoData.max_supply || 0,
      ath: coinGeckoData.ath,
      ath_change_percentage: coinGeckoData.ath_change_percentage,
      ath_date: coinGeckoData.ath_date,
      atl: coinGeckoData.atl,
      atl_change_percentage: coinGeckoData.atl_change_percentage,
      atl_date: coinGeckoData.atl_date,
      last_updated: coinGeckoData.last_updated,
    };
  }
}
