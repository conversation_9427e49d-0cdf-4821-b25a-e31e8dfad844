import { EtherscanService } from './EtherscanService';
import { BSCScanService } from './BSCScanService';
import { PriceService } from './PriceService';
import { 
  ChainTransaction, 
  WhaleAddress, 
  ChainAlert, 
  Blockchain, 
  ChainAlertType, 
  AlertSeverity 
} from '../types';
import logger from '../config/logger';
import prisma from '../config/database';

export interface MonitoringConfig {
  largeTransactionThreshold: {
    ethereum: number; // ETH
    bsc: number; // BNB
  };
  whaleBalanceThreshold: {
    ethereum: number; // ETH
    bsc: number; // BNB
  };
  monitoringInterval: number; // minutes
}

export interface MonitoringResult {
  largeTransactions: ChainTransaction[];
  whaleMovements: ChainTransaction[];
  alerts: ChainAlert[];
  errors: string[];
}

export class ChainMonitoringService {
  private etherscanService: EtherscanService;
  private bscScanService: BSCScanService;
  private priceService: PriceService;
  private config: MonitoringConfig;

  constructor() {
    this.etherscanService = new EtherscanService();
    this.bscScanService = new BSCScanService();
    this.priceService = new PriceService();
    
    this.config = {
      largeTransactionThreshold: {
        ethereum: 100, // 100 ETH
        bsc: 1000, // 1000 BNB
      },
      whaleBalanceThreshold: {
        ethereum: 1000, // 1000 ETH
        bsc: 10000, // 10000 BNB
      },
      monitoringInterval: 5, // 5 minutes
    };
  }

  async monitorEthereumTransactions(): Promise<{
    largeTransactions: ChainTransaction[];
    alerts: ChainAlert[];
  }> {
    try {
      const result = {
        largeTransactions: [] as ChainTransaction[],
        alerts: [] as ChainAlert[],
      };

      // Get whale addresses to monitor
      const whaleAddresses = await this.etherscanService.getTopEthereumAddresses();
      
      for (const address of whaleAddresses) {
        try {
          // Get recent transactions for this address
          const transactions = await this.etherscanService.getTransactions(address, 0, 99999999, 1, 50);
          
          for (const tx of transactions) {
            const valueEth = this.etherscanService.weiToEther(tx.value);
            
            // Check if it's a large transaction
            if (valueEth >= this.config.largeTransactionThreshold.ethereum) {
              const valueUSD = await this.etherscanService.getUSDValue(valueEth);
              
              // Create chain transaction record
              const chainTx = await this.createChainTransaction({
                hash: tx.hash,
                fromAddress: tx.from,
                toAddress: tx.to,
                value: tx.value,
                valueUSD,
                blockchain: Blockchain.ETHEREUM,
                blockNumber: BigInt(tx.blockNumber),
                timestamp: new Date(parseInt(tx.timeStamp) * 1000),
                isLargeTransaction: true,
              });

              result.largeTransactions.push(chainTx);

              // Create alert for large transaction
              const alert = await this.createChainAlert({
                alertType: ChainAlertType.LARGE_TRANSACTION,
                blockchain: Blockchain.ETHEREUM,
                description: `大额ETH转账：${valueEth.toFixed(2)} ETH ($${valueUSD.toFixed(2)})`,
                data: {
                  hash: tx.hash,
                  from: tx.from,
                  to: tx.to,
                  valueEth,
                  valueUSD,
                  blockNumber: tx.blockNumber,
                },
                severity: valueEth >= 1000 ? AlertSeverity.HIGH : AlertSeverity.MEDIUM,
              });

              result.alerts.push(alert);
            }
          }
        } catch (error) {
          logger.error(`Error monitoring address ${address}:`, error);
        }
      }

      return result;
    } catch (error) {
      logger.error('Error monitoring Ethereum transactions:', error);
      throw error;
    }
  }

  async monitorBSCTransactions(): Promise<{
    largeTransactions: ChainTransaction[];
    alerts: ChainAlert[];
  }> {
    try {
      const result = {
        largeTransactions: [] as ChainTransaction[],
        alerts: [] as ChainAlert[],
      };

      const whaleAddresses = await this.bscScanService.getTopBSCAddresses();
      
      for (const address of whaleAddresses) {
        try {
          const transactions = await this.bscScanService.getTransactions(address, 0, 99999999, 1, 50);
          
          for (const tx of transactions) {
            const valueBNB = this.bscScanService.weiToEther(tx.value);
            
            if (valueBNB >= this.config.largeTransactionThreshold.bsc) {
              // Get BNB price in USD (you'd integrate with your price service)
              const bnbPriceUSD = 300; // Placeholder
              const valueUSD = valueBNB * bnbPriceUSD;
              
              const chainTx = await this.createChainTransaction({
                hash: tx.hash,
                fromAddress: tx.from,
                toAddress: tx.to,
                value: tx.value,
                valueUSD,
                blockchain: Blockchain.BSC,
                blockNumber: BigInt(tx.blockNumber),
                timestamp: new Date(parseInt(tx.timeStamp) * 1000),
                isLargeTransaction: true,
              });

              result.largeTransactions.push(chainTx);

              const alert = await this.createChainAlert({
                alertType: ChainAlertType.LARGE_TRANSACTION,
                blockchain: Blockchain.BSC,
                description: `大额BNB转账：${valueBNB.toFixed(2)} BNB ($${valueUSD.toFixed(2)})`,
                data: {
                  hash: tx.hash,
                  from: tx.from,
                  to: tx.to,
                  valueBNB,
                  valueUSD,
                  blockNumber: tx.blockNumber,
                },
                severity: valueBNB >= 5000 ? AlertSeverity.HIGH : AlertSeverity.MEDIUM,
              });

              result.alerts.push(alert);
            }
          }
        } catch (error) {
          logger.error(`Error monitoring BSC address ${address}:`, error);
        }
      }

      return result;
    } catch (error) {
      logger.error('Error monitoring BSC transactions:', error);
      throw error;
    }
  }

  async updateWhaleBalances(): Promise<{
    updated: number;
    alerts: ChainAlert[];
  }> {
    try {
      const result = {
        updated: 0,
        alerts: [] as ChainAlert[],
      };

      // Update Ethereum whale balances
      const ethAddresses = await this.etherscanService.getTopEthereumAddresses();
      const ethBalances = await this.etherscanService.getMultipleBalances(ethAddresses);
      
      for (const balance of ethBalances) {
        const balanceEth = this.etherscanService.weiToEther(balance.balance);
        const balanceUSD = await this.etherscanService.getUSDValue(balanceEth);
        
        await this.updateWhaleAddress({
          address: balance.account,
          blockchain: Blockchain.ETHEREUM,
          balance: balance.balance,
          balanceUSD,
        });
        
        result.updated++;
      }

      // Update BSC whale balances
      const bscAddresses = await this.bscScanService.getTopBSCAddresses();
      
      for (const address of bscAddresses) {
        try {
          const balance = await this.bscScanService.getAccountBalance(address);
          const balanceBNB = this.bscScanService.weiToEther(balance);
          const bnbPriceUSD = 300; // Placeholder
          const balanceUSD = balanceBNB * bnbPriceUSD;
          
          await this.updateWhaleAddress({
            address,
            blockchain: Blockchain.BSC,
            balance,
            balanceUSD,
          });
          
          result.updated++;
        } catch (error) {
          logger.error(`Error updating BSC balance for ${address}:`, error);
        }
      }

      return result;
    } catch (error) {
      logger.error('Error updating whale balances:', error);
      throw error;
    }
  }

  private async createChainTransaction(data: {
    hash: string;
    fromAddress: string;
    toAddress: string;
    value: string;
    valueUSD: number;
    blockchain: Blockchain;
    blockNumber: bigint;
    timestamp: Date;
    isLargeTransaction: boolean;
  }): Promise<ChainTransaction> {
    try {
      return await prisma.chainTransaction.create({
        data,
      });
    } catch (error) {
      // Handle duplicate transactions
      if (error instanceof Error && error.message.includes('Unique constraint')) {
        logger.debug(`Transaction ${data.hash} already exists`);
        const existing = await prisma.chainTransaction.findUnique({
          where: { hash: data.hash },
        });
        return existing!;
      }
      throw error;
    }
  }

  private async createChainAlert(data: {
    alertType: ChainAlertType;
    blockchain: Blockchain;
    description: string;
    data: Record<string, any>;
    severity: AlertSeverity;
    coinId?: string;
  }): Promise<ChainAlert> {
    return await prisma.chainAlert.create({
      data,
    });
  }

  private async updateWhaleAddress(data: {
    address: string;
    blockchain: Blockchain;
    balance: string;
    balanceUSD: number;
  }): Promise<WhaleAddress> {
    try {
      return await prisma.whaleAddress.upsert({
        where: {
          address_blockchain: {
            address: data.address,
            blockchain: data.blockchain,
          },
        },
        update: {
          balance: data.balance,
          balanceUSD: data.balanceUSD,
          lastActivity: new Date(),
        },
        create: {
          address: data.address,
          blockchain: data.blockchain,
          balance: data.balance,
          balanceUSD: data.balanceUSD,
          lastActivity: new Date(),
        },
      });
    } catch (error) {
      logger.error('Error updating whale address:', error);
      throw error;
    }
  }

  async getRecentAlerts(hours = 24): Promise<ChainAlert[]> {
    try {
      const since = new Date(Date.now() - hours * 60 * 60 * 1000);
      
      return await prisma.chainAlert.findMany({
        where: {
          createdAt: { gte: since },
        },
        orderBy: { createdAt: 'desc' },
        take: 100,
      });
    } catch (error) {
      logger.error('Error getting recent alerts:', error);
      throw error;
    }
  }

  async getWhaleAddresses(blockchain?: Blockchain): Promise<WhaleAddress[]> {
    try {
      return await prisma.whaleAddress.findMany({
        where: blockchain ? { blockchain } : undefined,
        orderBy: { balanceUSD: 'desc' },
        take: 50,
      });
    } catch (error) {
      logger.error('Error getting whale addresses:', error);
      throw error;
    }
  }

  async runFullMonitoring(): Promise<MonitoringResult> {
    try {
      logger.info('Starting full chain monitoring cycle');
      
      const result: MonitoringResult = {
        largeTransactions: [],
        whaleMovements: [],
        alerts: [],
        errors: [],
      };

      // Monitor Ethereum
      try {
        const ethResult = await this.monitorEthereumTransactions();
        result.largeTransactions.push(...ethResult.largeTransactions);
        result.alerts.push(...ethResult.alerts);
      } catch (error) {
        const errorMsg = `Ethereum monitoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        logger.error(errorMsg);
        result.errors.push(errorMsg);
      }

      // Monitor BSC
      try {
        const bscResult = await this.monitorBSCTransactions();
        result.largeTransactions.push(...bscResult.largeTransactions);
        result.alerts.push(...bscResult.alerts);
      } catch (error) {
        const errorMsg = `BSC monitoring failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        logger.error(errorMsg);
        result.errors.push(errorMsg);
      }

      // Update whale balances
      try {
        const whaleResult = await this.updateWhaleBalances();
        result.alerts.push(...whaleResult.alerts);
      } catch (error) {
        const errorMsg = `Whale balance update failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
        logger.error(errorMsg);
        result.errors.push(errorMsg);
      }

      logger.info(`Chain monitoring completed: ${result.largeTransactions.length} large transactions, ${result.alerts.length} alerts, ${result.errors.length} errors`);
      return result;

    } catch (error) {
      logger.error('Error in full chain monitoring:', error);
      throw error;
    }
  }
}
