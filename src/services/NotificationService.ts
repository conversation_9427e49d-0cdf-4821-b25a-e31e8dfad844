import { Telegraf } from 'telegraf';
import { NotificationModel } from '../models/NotificationModel';
import { SubscriptionService } from './SubscriptionService';
import { TechnicalAnalysisService } from './TechnicalAnalysisService';
import { ChainMonitoringService } from './ChainMonitoringService';
import { PriceService } from './PriceService';
import { 
  NotificationMessage, 
  NotificationType, 
  NotificationPriority, 
  PushFrequency,
  TechnicalSignal,
  ChainAlert 
} from '../types';
import config from '../config';
import logger from '../config/logger';

export interface NotificationResult {
  sent: number;
  failed: number;
  errors: string[];
}

export interface NotificationTemplate {
  title: string;
  message: string;
  priority: NotificationPriority;
}

export class NotificationService {
  private bot: Telegraf;
  private notificationModel: NotificationModel;
  private subscriptionService: SubscriptionService;
  private technicalAnalysisService: TechnicalAnalysisService;
  private chainMonitoringService: ChainMonitoringService;
  private priceService: PriceService;

  constructor() {
    this.bot = new Telegraf(config.telegram.botToken);
    this.notificationModel = new NotificationModel();
    this.subscriptionService = new SubscriptionService();
    this.technicalAnalysisService = new TechnicalAnalysisService();
    this.chainMonitoringService = new ChainMonitoringService();
    this.priceService = new PriceService();
  }

  async sendNotification(userId: string, notification: NotificationTemplate): Promise<boolean> {
    try {
      // Create notification record
      const notificationRecord = await this.notificationModel.create({
        userId,
        type: NotificationType.SYSTEM_NOTIFICATION,
        title: notification.title,
        message: notification.message,
        priority: notification.priority,
      });

      // Get user's telegram ID
      const user = await this.getUserById(userId);
      if (!user) {
        throw new Error('User not found');
      }

      // Send via Telegram
      await this.bot.telegram.sendMessage(user.telegramId, notification.message, {
        parse_mode: 'Markdown',
      });

      // Mark as sent
      await this.notificationModel.markAsSent(notificationRecord.id);
      
      logger.info(`Notification sent to user ${userId}`);
      return true;

    } catch (error) {
      logger.error(`Failed to send notification to user ${userId}:`, error);
      return false;
    }
  }

  async sendTechnicalSignalNotification(signal: TechnicalSignal): Promise<NotificationResult> {
    try {
      const result: NotificationResult = {
        sent: 0,
        failed: 0,
        errors: [],
      };

      // Get users subscribed to this coin with technical signals enabled
      const subscriptions = await this.subscriptionService.getCoinSubscriptions(signal.coinId, true);
      const eligibleSubscriptions = subscriptions.filter(sub => sub.enableTechnicalSignals);

      for (const subscription of eligibleSubscriptions) {
        try {
          const template = this.createTechnicalSignalTemplate(signal, subscription.coin.symbol);
          const success = await this.sendNotification(subscription.userId, template);
          
          if (success) {
            result.sent++;
          } else {
            result.failed++;
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`Failed to send to user ${subscription.userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      logger.info(`Technical signal notification completed: ${result.sent} sent, ${result.failed} failed`);
      return result;

    } catch (error) {
      logger.error('Error sending technical signal notifications:', error);
      throw error;
    }
  }

  async sendChainAlertNotification(alert: ChainAlert): Promise<NotificationResult> {
    try {
      const result: NotificationResult = {
        sent: 0,
        failed: 0,
        errors: [],
      };

      // Get users with chain alerts enabled
      let subscriptions;
      if (alert.coinId) {
        subscriptions = await this.subscriptionService.getCoinSubscriptions(alert.coinId, true);
      } else {
        // For general chain alerts, send to all users with chain monitoring enabled
        subscriptions = await this.subscriptionService.getAllActiveSubscriptions();
      }

      const eligibleSubscriptions = subscriptions.filter(sub => sub.enableChainAlerts);

      for (const subscription of eligibleSubscriptions) {
        try {
          const template = this.createChainAlertTemplate(alert);
          const success = await this.sendNotification(subscription.userId, template);
          
          if (success) {
            result.sent++;
          } else {
            result.failed++;
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`Failed to send to user ${subscription.userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      logger.info(`Chain alert notification completed: ${result.sent} sent, ${result.failed} failed`);
      return result;

    } catch (error) {
      logger.error('Error sending chain alert notifications:', error);
      throw error;
    }
  }

  async sendScheduledNotifications(frequency: string): Promise<NotificationResult> {
    try {
      const result: NotificationResult = {
        sent: 0,
        failed: 0,
        errors: [],
      };

      let pushFrequency: PushFrequency;
      switch (frequency) {
        case 'HOURLY':
          pushFrequency = PushFrequency.HOURLY;
          break;
        case 'EVERY_4_HOURS':
          pushFrequency = PushFrequency.EVERY_4_HOURS;
          break;
        case 'DAILY':
          pushFrequency = PushFrequency.DAILY;
          break;
        case 'WEEKLY':
          pushFrequency = PushFrequency.WEEKLY;
          break;
        default:
          throw new Error(`Invalid frequency: ${frequency}`);
      }

      // Get subscriptions for this frequency
      const subscriptions = await this.subscriptionService.getSubscriptionsByFrequency(pushFrequency);

      // Group by user to send combined updates
      const userSubscriptions = new Map<string, typeof subscriptions>();
      for (const subscription of subscriptions) {
        if (!userSubscriptions.has(subscription.userId)) {
          userSubscriptions.set(subscription.userId, []);
        }
        userSubscriptions.get(subscription.userId)!.push(subscription);
      }

      for (const [userId, userSubs] of userSubscriptions) {
        try {
          const template = await this.createScheduledUpdateTemplate(userSubs, frequency);
          const success = await this.sendNotification(userId, template);
          
          if (success) {
            result.sent++;
          } else {
            result.failed++;
          }
        } catch (error) {
          result.failed++;
          result.errors.push(`Failed to send to user ${userId}: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      logger.info(`Scheduled notifications (${frequency}) completed: ${result.sent} sent, ${result.failed} failed`);
      return result;

    } catch (error) {
      logger.error(`Error sending scheduled notifications (${frequency}):`, error);
      throw error;
    }
  }

  private createTechnicalSignalTemplate(signal: TechnicalSignal, symbol: string): NotificationTemplate {
    const signalEmoji = this.getSignalEmoji(signal.signalType);
    const strengthEmoji = this.getStrengthEmoji(signal.strength);
    
    return {
      title: `${signalEmoji} ${symbol} 技术信号`,
      message: `
${signalEmoji} **${symbol} 技术信号** ${strengthEmoji}

📊 **信号类型**: ${this.getSignalTypeText(signal.signalType)}
💪 **信号强度**: ${this.getStrengthText(signal.strength)}
📝 **描述**: ${signal.description}

📈 **技术指标**:
• MACD: ${signal.macdValue.toFixed(4)}
• 信号线: ${signal.macdSignal.toFixed(4)}
• 柱状图: ${signal.macdHistogram.toFixed(4)}
• RSI: ${signal.rsiValue.toFixed(2)}

${signal.isPulseSignal ? '🔥 **MACD脉冲信号!**' : ''}

⏰ ${new Date(signal.createdAt).toLocaleString('zh-CN')}
`,
      priority: signal.strength === 'VERY_STRONG' ? NotificationPriority.HIGH : NotificationPriority.NORMAL,
    };
  }

  private createChainAlertTemplate(alert: ChainAlert): NotificationTemplate {
    const alertEmoji = this.getAlertEmoji(alert.alertType);
    const severityEmoji = this.getSeverityEmoji(alert.severity);
    
    return {
      title: `${alertEmoji} 链上活动提醒`,
      message: `
${alertEmoji} **链上活动提醒** ${severityEmoji}

⛓️ **区块链**: ${alert.blockchain}
🚨 **类型**: ${this.getAlertTypeText(alert.alertType)}
📝 **描述**: ${alert.description}

⏰ ${new Date(alert.createdAt).toLocaleString('zh-CN')}
`,
      priority: alert.severity === 'HIGH' || alert.severity === 'CRITICAL' ? NotificationPriority.HIGH : NotificationPriority.NORMAL,
    };
  }

  private async createScheduledUpdateTemplate(subscriptions: any[], frequency: string): Promise<NotificationTemplate> {
    const frequencyText = this.getFrequencyText(frequency);
    const coins = subscriptions.map(sub => sub.coin.symbol).join(', ');
    
    // Get recent signals for subscribed coins
    const recentSignals = await this.technicalAnalysisService.getRecentSignals(24);
    const userSignals = recentSignals.filter(signal => 
      subscriptions.some(sub => sub.coinId === signal.coinId)
    );

    let signalsText = '';
    if (userSignals.length > 0) {
      signalsText = '\n📊 **最新信号**:\n';
      userSignals.slice(0, 5).forEach(signal => {
        const coin = subscriptions.find(sub => sub.coinId === signal.coinId)?.coin;
        signalsText += `• ${coin?.symbol}: ${this.getSignalTypeText(signal.signalType)} (${this.getStrengthText(signal.strength)})\n`;
      });
    }

    return {
      title: `📈 ${frequencyText}市场更新`,
      message: `
📈 **${frequencyText}市场更新**

👀 **关注币种**: ${coins}

${signalsText}

💡 使用 /price <币种> 查看详细价格信息
⚙️ 使用 /settings 调整推送设置

⏰ ${new Date().toLocaleString('zh-CN')}
`,
      priority: NotificationPriority.NORMAL,
    };
  }

  // Helper methods
  private async getUserById(userId: string): Promise<any> {
    // This should integrate with your UserService
    // For now, return a placeholder
    return { telegramId: 123456789 }; // Placeholder
  }

  private getSignalEmoji(signalType: string): string {
    switch (signalType) {
      case 'BUY':
      case 'STRONG_BUY':
        return '🟢';
      case 'SELL':
      case 'STRONG_SELL':
        return '🔴';
      case 'HOLD':
        return '🟡';
      default:
        return '⚪';
    }
  }

  private getStrengthEmoji(strength: string): string {
    switch (strength) {
      case 'VERY_STRONG':
        return '🔥🔥🔥';
      case 'STRONG':
        return '🔥🔥';
      case 'MODERATE':
        return '🔥';
      case 'WEAK':
        return '💨';
      default:
        return '';
    }
  }

  private getAlertEmoji(alertType: string): string {
    switch (alertType) {
      case 'LARGE_TRANSACTION':
        return '💰';
      case 'WHALE_MOVEMENT':
        return '🐋';
      case 'EXCHANGE_INFLOW':
        return '📥';
      case 'EXCHANGE_OUTFLOW':
        return '📤';
      case 'UNUSUAL_VOLUME':
        return '📊';
      default:
        return '🚨';
    }
  }

  private getSeverityEmoji(severity: string): string {
    switch (severity) {
      case 'CRITICAL':
        return '🚨🚨🚨';
      case 'HIGH':
        return '🚨🚨';
      case 'MEDIUM':
        return '🚨';
      case 'LOW':
        return '💡';
      default:
        return '';
    }
  }

  private getSignalTypeText(signalType: string): string {
    const texts: Record<string, string> = {
      'BUY': '买入',
      'STRONG_BUY': '强烈买入',
      'SELL': '卖出',
      'STRONG_SELL': '强烈卖出',
      'HOLD': '持有',
    };
    return texts[signalType] || signalType;
  }

  private getStrengthText(strength: string): string {
    const texts: Record<string, string> = {
      'VERY_STRONG': '非常强',
      'STRONG': '强',
      'MODERATE': '中等',
      'WEAK': '弱',
    };
    return texts[strength] || strength;
  }

  private getAlertTypeText(alertType: string): string {
    const texts: Record<string, string> = {
      'LARGE_TRANSACTION': '大额转账',
      'WHALE_MOVEMENT': '巨鲸活动',
      'EXCHANGE_INFLOW': '交易所流入',
      'EXCHANGE_OUTFLOW': '交易所流出',
      'UNUSUAL_VOLUME': '异常交易量',
    };
    return texts[alertType] || alertType;
  }

  private getFrequencyText(frequency: string): string {
    const texts: Record<string, string> = {
      'HOURLY': '每小时',
      'EVERY_4_HOURS': '每4小时',
      'DAILY': '每日',
      'WEEKLY': '每周',
    };
    return texts[frequency] || frequency;
  }
}
