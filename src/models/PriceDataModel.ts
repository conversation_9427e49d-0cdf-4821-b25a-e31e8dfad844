import prisma from '../config/database';
import { PriceData, HistoricalPrice } from '../types';
import logger from '../config/logger';

export interface CreatePriceDataInput {
  coinId: string;
  price: number;
  priceChange24h: number;
  priceChangePercentage24h: number;
  marketCap: number;
  volume24h: number;
  timestamp?: Date;
}

export interface CreateHistoricalPriceInput {
  coinId: string;
  price: number;
  volume: number;
  marketCap: number;
  timestamp: Date;
}

export class PriceDataModel {
  async createPriceData(data: CreatePriceDataInput): Promise<PriceData> {
    try {
      const priceData = await prisma.priceData.create({
        data: {
          ...data,
          timestamp: data.timestamp || new Date(),
        },
        include: {
          coin: true,
        },
      });

      logger.debug(`Price data created for ${priceData.coin.symbol}: $${priceData.price}`);
      return priceData;
    } catch (error) {
      logger.error('Error creating price data:', error);
      throw error;
    }
  }

  async createHistoricalPrice(data: CreateHistoricalPriceInput): Promise<HistoricalPrice> {
    try {
      const historicalPrice = await prisma.historicalPrice.create({
        data,
        include: {
          coin: true,
        },
      });

      logger.debug(`Historical price created for ${historicalPrice.coin.symbol}: $${historicalPrice.price}`);
      return historicalPrice;
    } catch (error) {
      logger.error('Error creating historical price:', error);
      throw error;
    }
  }

  async getLatestPriceData(coinId: string): Promise<PriceData | null> {
    try {
      return await prisma.priceData.findFirst({
        where: { coinId },
        orderBy: { timestamp: 'desc' },
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error getting latest price data:', error);
      throw error;
    }
  }

  async getPriceDataByTimeRange(
    coinId: string,
    startTime: Date,
    endTime: Date
  ): Promise<PriceData[]> {
    try {
      return await prisma.priceData.findMany({
        where: {
          coinId,
          timestamp: {
            gte: startTime,
            lte: endTime,
          },
        },
        orderBy: { timestamp: 'asc' },
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error getting price data by time range:', error);
      throw error;
    }
  }

  async getHistoricalPrices(
    coinId: string,
    startTime: Date,
    endTime: Date,
    limit?: number
  ): Promise<HistoricalPrice[]> {
    try {
      return await prisma.historicalPrice.findMany({
        where: {
          coinId,
          timestamp: {
            gte: startTime,
            lte: endTime,
          },
        },
        orderBy: { timestamp: 'asc' },
        take: limit,
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error getting historical prices:', error);
      throw error;
    }
  }

  async getLatestPricesForAllCoins(): Promise<PriceData[]> {
    try {
      // Get the latest price data for each coin
      const latestPrices = await prisma.$queryRaw<PriceData[]>`
        SELECT DISTINCT ON (coin_id) *
        FROM price_data
        ORDER BY coin_id, timestamp DESC
      `;

      return latestPrices;
    } catch (error) {
      logger.error('Error getting latest prices for all coins:', error);
      throw error;
    }
  }

  async bulkCreatePriceData(data: CreatePriceDataInput[]): Promise<number> {
    try {
      const result = await prisma.priceData.createMany({
        data: data.map(item => ({
          ...item,
          timestamp: item.timestamp || new Date(),
        })),
        skipDuplicates: true,
      });

      logger.info(`Bulk created ${result.count} price data records`);
      return result.count;
    } catch (error) {
      logger.error('Error bulk creating price data:', error);
      throw error;
    }
  }

  async bulkCreateHistoricalPrices(data: CreateHistoricalPriceInput[]): Promise<number> {
    try {
      const result = await prisma.historicalPrice.createMany({
        data,
        skipDuplicates: true,
      });

      logger.info(`Bulk created ${result.count} historical price records`);
      return result.count;
    } catch (error) {
      logger.error('Error bulk creating historical prices:', error);
      throw error;
    }
  }

  async deletePriceDataOlderThan(days: number): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const result = await prisma.priceData.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      logger.info(`Deleted ${result.count} old price data records`);
      return result.count;
    } catch (error) {
      logger.error('Error deleting old price data:', error);
      throw error;
    }
  }

  async deleteHistoricalPricesOlderThan(days: number): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const result = await prisma.historicalPrice.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      logger.info(`Deleted ${result.count} old historical price records`);
      return result.count;
    } catch (error) {
      logger.error('Error deleting old historical prices:', error);
      throw error;
    }
  }

  async getPriceChangeStats(coinId: string, hours: number): Promise<{
    startPrice: number;
    endPrice: number;
    change: number;
    changePercentage: number;
  } | null> {
    try {
      const endTime = new Date();
      const startTime = new Date(endTime.getTime() - hours * 60 * 60 * 1000);

      const [startPrice, endPrice] = await Promise.all([
        prisma.priceData.findFirst({
          where: {
            coinId,
            timestamp: { gte: startTime },
          },
          orderBy: { timestamp: 'asc' },
        }),
        prisma.priceData.findFirst({
          where: { coinId },
          orderBy: { timestamp: 'desc' },
        }),
      ]);

      if (!startPrice || !endPrice) {
        return null;
      }

      const change = endPrice.price - startPrice.price;
      const changePercentage = (change / startPrice.price) * 100;

      return {
        startPrice: startPrice.price,
        endPrice: endPrice.price,
        change,
        changePercentage,
      };
    } catch (error) {
      logger.error('Error getting price change stats:', error);
      throw error;
    }
  }
}
