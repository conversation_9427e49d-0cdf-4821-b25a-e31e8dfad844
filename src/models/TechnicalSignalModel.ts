import prisma from '../config/database';
import { TechnicalSignal, SignalType, SignalStrength } from '../types';
import logger from '../config/logger';

export interface CreateTechnicalSignalInput {
  coinId: string;
  signalType: SignalType;
  strength: SignalStrength;
  description: string;
  macdValue: number;
  macdSignal: number;
  macdHistogram: number;
  rsiValue: number;
  isPulseSignal?: boolean;
}

export class TechnicalSignalModel {
  async create(data: CreateTechnicalSignalInput): Promise<TechnicalSignal> {
    try {
      const signal = await prisma.technicalSignal.create({
        data,
        include: {
          coin: true,
        },
      });

      logger.info(`Technical signal created: ${signal.signalType} for ${signal.coin.symbol}`);
      return signal;
    } catch (error) {
      logger.error('Error creating technical signal:', error);
      throw error;
    }
  }

  async findById(id: string): Promise<TechnicalSignal | null> {
    try {
      return await prisma.technicalSignal.findUnique({
        where: { id },
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error finding technical signal by ID:', error);
      throw error;
    }
  }

  async findByCoinId(coinId: string, limit = 50): Promise<TechnicalSignal[]> {
    try {
      return await prisma.technicalSignal.findMany({
        where: { coinId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error finding technical signals by coin ID:', error);
      throw error;
    }
  }

  async findBySignalType(signalType: SignalType, limit = 100): Promise<TechnicalSignal[]> {
    try {
      return await prisma.technicalSignal.findMany({
        where: { signalType },
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error finding technical signals by type:', error);
      throw error;
    }
  }

  async findRecentSignals(hours = 24, limit = 100): Promise<TechnicalSignal[]> {
    try {
      const since = new Date(Date.now() - hours * 60 * 60 * 1000);
      
      return await prisma.technicalSignal.findMany({
        where: {
          createdAt: { gte: since },
        },
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error finding recent technical signals:', error);
      throw error;
    }
  }

  async findPulseSignals(hours = 24): Promise<TechnicalSignal[]> {
    try {
      const since = new Date(Date.now() - hours * 60 * 60 * 1000);
      
      return await prisma.technicalSignal.findMany({
        where: {
          isPulseSignal: true,
          createdAt: { gte: since },
        },
        orderBy: { createdAt: 'desc' },
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error finding pulse signals:', error);
      throw error;
    }
  }

  async findStrongSignals(hours = 24): Promise<TechnicalSignal[]> {
    try {
      const since = new Date(Date.now() - hours * 60 * 60 * 1000);
      
      return await prisma.technicalSignal.findMany({
        where: {
          strength: {
            in: [SignalStrength.STRONG, SignalStrength.VERY_STRONG],
          },
          createdAt: { gte: since },
        },
        orderBy: { createdAt: 'desc' },
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error finding strong signals:', error);
      throw error;
    }
  }

  async getLatestSignalForCoin(coinId: string): Promise<TechnicalSignal | null> {
    try {
      return await prisma.technicalSignal.findFirst({
        where: { coinId },
        orderBy: { createdAt: 'desc' },
        include: {
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error getting latest signal for coin:', error);
      throw error;
    }
  }

  async bulkCreate(data: CreateTechnicalSignalInput[]): Promise<number> {
    try {
      const result = await prisma.technicalSignal.createMany({
        data,
        skipDuplicates: true,
      });

      logger.info(`Bulk created ${result.count} technical signals`);
      return result.count;
    } catch (error) {
      logger.error('Error bulk creating technical signals:', error);
      throw error;
    }
  }

  async deleteOldSignals(days: number): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const result = await prisma.technicalSignal.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
        },
      });

      logger.info(`Deleted ${result.count} old technical signals`);
      return result.count;
    } catch (error) {
      logger.error('Error deleting old technical signals:', error);
      throw error;
    }
  }

  async getSignalStats(coinId?: string): Promise<{
    total: number;
    byType: Record<SignalType, number>;
    byStrength: Record<SignalStrength, number>;
    pulseSignals: number;
  }> {
    try {
      const where = coinId ? { coinId } : {};

      const [total, byType, byStrength, pulseSignals] = await Promise.all([
        prisma.technicalSignal.count({ where }),
        prisma.technicalSignal.groupBy({
          by: ['signalType'],
          where,
          _count: { signalType: true },
        }),
        prisma.technicalSignal.groupBy({
          by: ['strength'],
          where,
          _count: { strength: true },
        }),
        prisma.technicalSignal.count({
          where: { ...where, isPulseSignal: true },
        }),
      ]);

      const typeStats = Object.values(SignalType).reduce((acc, type) => {
        acc[type] = byType.find(item => item.signalType === type)?._count.signalType || 0;
        return acc;
      }, {} as Record<SignalType, number>);

      const strengthStats = Object.values(SignalStrength).reduce((acc, strength) => {
        acc[strength] = byStrength.find(item => item.strength === strength)?._count.strength || 0;
        return acc;
      }, {} as Record<SignalStrength, number>);

      return {
        total,
        byType: typeStats,
        byStrength: strengthStats,
        pulseSignals,
      };
    } catch (error) {
      logger.error('Error getting signal stats:', error);
      throw error;
    }
  }
}
