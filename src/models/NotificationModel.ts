import prisma from '../config/database';
import { NotificationMessage, NotificationType, NotificationPriority } from '../types';
import logger from '../config/logger';

export interface CreateNotificationInput {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority?: NotificationPriority;
  scheduledAt?: Date;
}

export class NotificationModel {
  async create(data: CreateNotificationInput): Promise<NotificationMessage> {
    try {
      const notification = await prisma.notificationMessage.create({
        data: {
          ...data,
          priority: data.priority || NotificationPriority.NORMAL,
        },
        include: {
          user: true,
        },
      });

      logger.info(`Notification created: ${notification.type} for user ${notification.userId}`);
      return notification;
    } catch (error) {
      logger.error('Error creating notification:', error);
      throw error;
    }
  }

  async findById(id: string): Promise<NotificationMessage | null> {
    try {
      return await prisma.notificationMessage.findUnique({
        where: { id },
        include: {
          user: true,
        },
      });
    } catch (error) {
      logger.error('Error finding notification by ID:', error);
      throw error;
    }
  }

  async findByUserId(userId: string, limit = 50): Promise<NotificationMessage[]> {
    try {
      return await prisma.notificationMessage.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: {
          user: true,
        },
      });
    } catch (error) {
      logger.error('Error finding notifications by user ID:', error);
      throw error;
    }
  }

  async findUnreadByUserId(userId: string): Promise<NotificationMessage[]> {
    try {
      return await prisma.notificationMessage.findMany({
        where: { 
          userId,
          isRead: false,
        },
        orderBy: { createdAt: 'desc' },
        include: {
          user: true,
        },
      });
    } catch (error) {
      logger.error('Error finding unread notifications by user ID:', error);
      throw error;
    }
  }

  async findPendingNotifications(): Promise<NotificationMessage[]> {
    try {
      const now = new Date();
      
      return await prisma.notificationMessage.findMany({
        where: {
          isSent: false,
          OR: [
            { scheduledAt: null },
            { scheduledAt: { lte: now } },
          ],
        },
        orderBy: [
          { priority: 'desc' },
          { createdAt: 'asc' },
        ],
        include: {
          user: true,
        },
      });
    } catch (error) {
      logger.error('Error finding pending notifications:', error);
      throw error;
    }
  }

  async findByType(type: NotificationType, limit = 100): Promise<NotificationMessage[]> {
    try {
      return await prisma.notificationMessage.findMany({
        where: { type },
        orderBy: { createdAt: 'desc' },
        take: limit,
        include: {
          user: true,
        },
      });
    } catch (error) {
      logger.error('Error finding notifications by type:', error);
      throw error;
    }
  }

  async markAsRead(id: string): Promise<NotificationMessage> {
    try {
      const notification = await prisma.notificationMessage.update({
        where: { id },
        data: { isRead: true },
        include: {
          user: true,
        },
      });

      logger.debug(`Notification marked as read: ${id}`);
      return notification;
    } catch (error) {
      logger.error('Error marking notification as read:', error);
      throw error;
    }
  }

  async markAsSent(id: string): Promise<NotificationMessage> {
    try {
      const notification = await prisma.notificationMessage.update({
        where: { id },
        data: { 
          isSent: true,
          sentAt: new Date(),
        },
        include: {
          user: true,
        },
      });

      logger.debug(`Notification marked as sent: ${id}`);
      return notification;
    } catch (error) {
      logger.error('Error marking notification as sent:', error);
      throw error;
    }
  }

  async markAllAsReadForUser(userId: string): Promise<number> {
    try {
      const result = await prisma.notificationMessage.updateMany({
        where: { 
          userId,
          isRead: false,
        },
        data: { isRead: true },
      });

      logger.info(`Marked ${result.count} notifications as read for user ${userId}`);
      return result.count;
    } catch (error) {
      logger.error('Error marking all notifications as read:', error);
      throw error;
    }
  }

  async bulkCreate(data: CreateNotificationInput[]): Promise<number> {
    try {
      const result = await prisma.notificationMessage.createMany({
        data: data.map(item => ({
          ...item,
          priority: item.priority || NotificationPriority.NORMAL,
        })),
        skipDuplicates: true,
      });

      logger.info(`Bulk created ${result.count} notifications`);
      return result.count;
    } catch (error) {
      logger.error('Error bulk creating notifications:', error);
      throw error;
    }
  }

  async deleteOldNotifications(days: number): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      const result = await prisma.notificationMessage.deleteMany({
        where: {
          createdAt: {
            lt: cutoffDate,
          },
          isRead: true,
          isSent: true,
        },
      });

      logger.info(`Deleted ${result.count} old notifications`);
      return result.count;
    } catch (error) {
      logger.error('Error deleting old notifications:', error);
      throw error;
    }
  }

  async getUnreadCount(userId: string): Promise<number> {
    try {
      return await prisma.notificationMessage.count({
        where: { 
          userId,
          isRead: false,
        },
      });
    } catch (error) {
      logger.error('Error getting unread count:', error);
      throw error;
    }
  }

  async getNotificationStats(userId?: string): Promise<{
    total: number;
    unread: number;
    sent: number;
    pending: number;
    byType: Record<NotificationType, number>;
    byPriority: Record<NotificationPriority, number>;
  }> {
    try {
      const where = userId ? { userId } : {};

      const [total, unread, sent, pending, byType, byPriority] = await Promise.all([
        prisma.notificationMessage.count({ where }),
        prisma.notificationMessage.count({ where: { ...where, isRead: false } }),
        prisma.notificationMessage.count({ where: { ...where, isSent: true } }),
        prisma.notificationMessage.count({ where: { ...where, isSent: false } }),
        prisma.notificationMessage.groupBy({
          by: ['type'],
          where,
          _count: { type: true },
        }),
        prisma.notificationMessage.groupBy({
          by: ['priority'],
          where,
          _count: { priority: true },
        }),
      ]);

      const typeStats = Object.values(NotificationType).reduce((acc, type) => {
        acc[type] = byType.find(item => item.type === type)?._count.type || 0;
        return acc;
      }, {} as Record<NotificationType, number>);

      const priorityStats = Object.values(NotificationPriority).reduce((acc, priority) => {
        acc[priority] = byPriority.find(item => item.priority === priority)?._count.priority || 0;
        return acc;
      }, {} as Record<NotificationPriority, number>);

      return {
        total,
        unread,
        sent,
        pending,
        byType: typeStats,
        byPriority: priorityStats,
      };
    } catch (error) {
      logger.error('Error getting notification stats:', error);
      throw error;
    }
  }
}
