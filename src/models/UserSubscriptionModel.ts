import prisma from '../config/database';
import { UserSubscription, PushFrequency } from '../types';
import logger from '../config/logger';

export interface CreateUserSubscriptionData {
  userId: string;
  coinId: string;
  enablePriceAlerts?: boolean;
  enableTechnicalSignals?: boolean;
  enableChainAlerts?: boolean;
  pushFrequency?: PushFrequency;
  priceChangeThreshold?: number;
  volumeChangeThreshold?: number;
}

export interface UpdateUserSubscriptionData {
  isActive?: boolean;
  enablePriceAlerts?: boolean;
  enableTechnicalSignals?: boolean;
  enableChainAlerts?: boolean;
  pushFrequency?: PushFrequency;
  priceChangeThreshold?: number;
  volumeChangeThreshold?: number;
}

export class UserSubscriptionModel {
  async create(data: CreateUserSubscriptionData): Promise<UserSubscription> {
    try {
      const subscription = await prisma.userSubscription.create({
        data,
        include: {
          user: true,
          coin: true,
        },
      });

      logger.info(`User subscription created: ${subscription.id} (User: ${data.userId}, Coin: ${data.coinId})`);
      return subscription;
    } catch (error) {
      logger.error('Error creating user subscription:', error);
      throw error;
    }
  }

  async findById(id: string): Promise<UserSubscription | null> {
    try {
      return await prisma.userSubscription.findUnique({
        where: { id },
        include: {
          user: true,
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error finding user subscription by ID:', error);
      throw error;
    }
  }

  async findByUserAndCoin(userId: string, coinId: string): Promise<UserSubscription | null> {
    try {
      return await prisma.userSubscription.findUnique({
        where: {
          userId_coinId: {
            userId,
            coinId,
          },
        },
        include: {
          user: true,
          coin: true,
        },
      });
    } catch (error) {
      logger.error('Error finding user subscription by user and coin:', error);
      throw error;
    }
  }

  async findByUserId(userId: string, isActive = true): Promise<UserSubscription[]> {
    try {
      return await prisma.userSubscription.findMany({
        where: { 
          userId,
          isActive,
        },
        include: {
          user: true,
          coin: true,
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      logger.error('Error finding user subscriptions by user ID:', error);
      throw error;
    }
  }

  async findByCoinId(coinId: string, isActive = true): Promise<UserSubscription[]> {
    try {
      return await prisma.userSubscription.findMany({
        where: { 
          coinId,
          isActive,
        },
        include: {
          user: true,
          coin: true,
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      logger.error('Error finding user subscriptions by coin ID:', error);
      throw error;
    }
  }

  async findByPushFrequency(frequency: PushFrequency, isActive = true): Promise<UserSubscription[]> {
    try {
      return await prisma.userSubscription.findMany({
        where: { 
          pushFrequency: frequency,
          isActive,
        },
        include: {
          user: true,
          coin: true,
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      logger.error('Error finding user subscriptions by push frequency:', error);
      throw error;
    }
  }

  async findActiveSubscriptions(): Promise<UserSubscription[]> {
    try {
      return await prisma.userSubscription.findMany({
        where: { 
          isActive: true,
          user: { isActive: true },
          coin: { isActive: true },
        },
        include: {
          user: true,
          coin: true,
        },
        orderBy: { createdAt: 'desc' },
      });
    } catch (error) {
      logger.error('Error finding active user subscriptions:', error);
      throw error;
    }
  }

  async update(id: string, data: UpdateUserSubscriptionData): Promise<UserSubscription> {
    try {
      const subscription = await prisma.userSubscription.update({
        where: { id },
        data,
        include: {
          user: true,
          coin: true,
        },
      });

      logger.info(`User subscription updated: ${subscription.id}`);
      return subscription;
    } catch (error) {
      logger.error('Error updating user subscription:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await prisma.userSubscription.delete({
        where: { id },
      });

      logger.info(`User subscription deleted: ${id}`);
    } catch (error) {
      logger.error('Error deleting user subscription:', error);
      throw error;
    }
  }

  async deactivate(id: string): Promise<UserSubscription> {
    try {
      const subscription = await prisma.userSubscription.update({
        where: { id },
        data: { isActive: false },
        include: {
          user: true,
          coin: true,
        },
      });

      logger.info(`User subscription deactivated: ${subscription.id}`);
      return subscription;
    } catch (error) {
      logger.error('Error deactivating user subscription:', error);
      throw error;
    }
  }

  async activate(id: string): Promise<UserSubscription> {
    try {
      const subscription = await prisma.userSubscription.update({
        where: { id },
        data: { isActive: true },
        include: {
          user: true,
          coin: true,
        },
      });

      logger.info(`User subscription activated: ${subscription.id}`);
      return subscription;
    } catch (error) {
      logger.error('Error activating user subscription:', error);
      throw error;
    }
  }

  async getCountByUserId(userId: string): Promise<number> {
    try {
      return await prisma.userSubscription.count({
        where: { 
          userId,
          isActive: true,
        },
      });
    } catch (error) {
      logger.error('Error getting user subscription count:', error);
      throw error;
    }
  }

  async getCountByCoinId(coinId: string): Promise<number> {
    try {
      return await prisma.userSubscription.count({
        where: { 
          coinId,
          isActive: true,
        },
      });
    } catch (error) {
      logger.error('Error getting coin subscription count:', error);
      throw error;
    }
  }

  async getTotalActiveCount(): Promise<number> {
    try {
      return await prisma.userSubscription.count({
        where: { 
          isActive: true,
          user: { isActive: true },
          coin: { isActive: true },
        },
      });
    } catch (error) {
      logger.error('Error getting total active subscription count:', error);
      throw error;
    }
  }
}
