import prisma from '../config/database';
import { Cryptocurrency, Blockchain } from '../types';
import logger from '../config/logger';

export interface CreateCryptocurrencyData {
  symbol: string;
  name: string;
  coinGeckoId: string;
  contractAddress?: string;
  blockchain: Blockchain;
}

export interface UpdateCryptocurrencyData {
  name?: string;
  coinGeckoId?: string;
  contractAddress?: string;
  blockchain?: Blockchain;
  isActive?: boolean;
}

export class CryptocurrencyModel {
  async create(data: CreateCryptocurrencyData): Promise<Cryptocurrency> {
    try {
      const crypto = await prisma.cryptocurrency.create({
        data,
      });

      logger.info(`Cryptocurrency created: ${crypto.symbol} (${crypto.id})`);
      return crypto;
    } catch (error) {
      logger.error('Error creating cryptocurrency:', error);
      throw error;
    }
  }

  async findById(id: string): Promise<Cryptocurrency | null> {
    try {
      return await prisma.cryptocurrency.findUnique({
        where: { id },
      });
    } catch (error) {
      logger.error('Error finding cryptocurrency by ID:', error);
      throw error;
    }
  }

  async findBySymbol(symbol: string): Promise<Cryptocurrency | null> {
    try {
      return await prisma.cryptocurrency.findUnique({
        where: { symbol: symbol.toUpperCase() },
      });
    } catch (error) {
      logger.error('Error finding cryptocurrency by symbol:', error);
      throw error;
    }
  }

  async findByCoinGeckoId(coinGeckoId: string): Promise<Cryptocurrency | null> {
    try {
      return await prisma.cryptocurrency.findUnique({
        where: { coinGeckoId },
      });
    } catch (error) {
      logger.error('Error finding cryptocurrency by CoinGecko ID:', error);
      throw error;
    }
  }

  async findAll(isActive = true): Promise<Cryptocurrency[]> {
    try {
      return await prisma.cryptocurrency.findMany({
        where: { isActive },
        orderBy: { symbol: 'asc' },
      });
    } catch (error) {
      logger.error('Error finding all cryptocurrencies:', error);
      throw error;
    }
  }

  async findByBlockchain(blockchain: Blockchain): Promise<Cryptocurrency[]> {
    try {
      return await prisma.cryptocurrency.findMany({
        where: { 
          blockchain,
          isActive: true,
        },
        orderBy: { symbol: 'asc' },
      });
    } catch (error) {
      logger.error('Error finding cryptocurrencies by blockchain:', error);
      throw error;
    }
  }

  async update(id: string, data: UpdateCryptocurrencyData): Promise<Cryptocurrency> {
    try {
      const crypto = await prisma.cryptocurrency.update({
        where: { id },
        data,
      });

      logger.info(`Cryptocurrency updated: ${crypto.symbol} (${crypto.id})`);
      return crypto;
    } catch (error) {
      logger.error('Error updating cryptocurrency:', error);
      throw error;
    }
  }

  async delete(id: string): Promise<void> {
    try {
      await prisma.cryptocurrency.delete({
        where: { id },
      });

      logger.info(`Cryptocurrency deleted: ${id}`);
    } catch (error) {
      logger.error('Error deleting cryptocurrency:', error);
      throw error;
    }
  }

  async deactivate(id: string): Promise<Cryptocurrency> {
    try {
      const crypto = await prisma.cryptocurrency.update({
        where: { id },
        data: { isActive: false },
      });

      logger.info(`Cryptocurrency deactivated: ${crypto.symbol} (${crypto.id})`);
      return crypto;
    } catch (error) {
      logger.error('Error deactivating cryptocurrency:', error);
      throw error;
    }
  }

  async activate(id: string): Promise<Cryptocurrency> {
    try {
      const crypto = await prisma.cryptocurrency.update({
        where: { id },
        data: { isActive: true },
      });

      logger.info(`Cryptocurrency activated: ${crypto.symbol} (${crypto.id})`);
      return crypto;
    } catch (error) {
      logger.error('Error activating cryptocurrency:', error);
      throw error;
    }
  }

  async getCount(): Promise<number> {
    try {
      return await prisma.cryptocurrency.count({
        where: { isActive: true },
      });
    } catch (error) {
      logger.error('Error getting cryptocurrency count:', error);
      throw error;
    }
  }

  async search(query: string): Promise<Cryptocurrency[]> {
    try {
      return await prisma.cryptocurrency.findMany({
        where: {
          isActive: true,
          OR: [
            { symbol: { contains: query.toUpperCase(), mode: 'insensitive' } },
            { name: { contains: query, mode: 'insensitive' } },
            { coinGeckoId: { contains: query.toLowerCase(), mode: 'insensitive' } },
          ],
        },
        orderBy: { symbol: 'asc' },
        take: 20, // Limit results
      });
    } catch (error) {
      logger.error('Error searching cryptocurrencies:', error);
      throw error;
    }
  }

  async bulkCreate(data: CreateCryptocurrencyData[]): Promise<number> {
    try {
      const result = await prisma.cryptocurrency.createMany({
        data,
        skipDuplicates: true,
      });

      logger.info(`Bulk created ${result.count} cryptocurrencies`);
      return result.count;
    } catch (error) {
      logger.error('Error bulk creating cryptocurrencies:', error);
      throw error;
    }
  }
}
