import cron from 'node-cron';
import { PriceService } from '../services/PriceService';
import { TechnicalAnalysisService } from '../services/TechnicalAnalysisService';
import { ChainMonitoringService } from '../services/ChainMonitoringService';
import { NotificationService } from '../services/NotificationService';
import { DataInitializationService } from '../services/DataInitializationService';
import logger from '../config/logger';

export interface ScheduledTask {
  name: string;
  schedule: string;
  enabled: boolean;
  lastRun?: Date;
  nextRun?: Date;
  isRunning: boolean;
  task: () => Promise<void>;
}

export class TaskScheduler {
  private tasks: Map<string, ScheduledTask>;
  private priceService: PriceService;
  private technicalAnalysisService: TechnicalAnalysisService;
  private chainMonitoringService: ChainMonitoringService;
  private notificationService: NotificationService;
  private dataInitService: DataInitializationService;

  constructor() {
    this.tasks = new Map();
    this.priceService = new PriceService();
    this.technicalAnalysisService = new TechnicalAnalysisService();
    this.chainMonitoringService = new ChainMonitoringService();
    this.notificationService = new NotificationService();
    this.dataInitService = new DataInitializationService();

    this.setupTasks();
  }

  private setupTasks(): void {
    // Price update task - every 5 minutes
    this.addTask({
      name: 'price-update',
      schedule: '*/5 * * * *',
      enabled: true,
      isRunning: false,
      task: this.updatePrices.bind(this),
    });

    // Technical analysis - every 15 minutes
    this.addTask({
      name: 'technical-analysis',
      schedule: '*/15 * * * *',
      enabled: true,
      isRunning: false,
      task: this.runTechnicalAnalysis.bind(this),
    });

    // Chain monitoring - every 10 minutes
    this.addTask({
      name: 'chain-monitoring',
      schedule: '*/10 * * * *',
      enabled: true,
      isRunning: false,
      task: this.runChainMonitoring.bind(this),
    });

    // Hourly notifications
    this.addTask({
      name: 'hourly-notifications',
      schedule: '0 * * * *',
      enabled: true,
      isRunning: false,
      task: this.sendHourlyNotifications.bind(this),
    });

    // Daily notifications - at 9 AM
    this.addTask({
      name: 'daily-notifications',
      schedule: '0 9 * * *',
      enabled: true,
      isRunning: false,
      task: this.sendDailyNotifications.bind(this),
    });

    // Weekly notifications - Monday at 9 AM
    this.addTask({
      name: 'weekly-notifications',
      schedule: '0 9 * * 1',
      enabled: true,
      isRunning: false,
      task: this.sendWeeklyNotifications.bind(this),
    });

    // Data cleanup - daily at 2 AM
    this.addTask({
      name: 'data-cleanup',
      schedule: '0 2 * * *',
      enabled: true,
      isRunning: false,
      task: this.cleanupOldData.bind(this),
    });

    // System health check - every hour
    this.addTask({
      name: 'health-check',
      schedule: '0 * * * *',
      enabled: true,
      isRunning: false,
      task: this.systemHealthCheck.bind(this),
    });
  }

  private addTask(task: ScheduledTask): void {
    this.tasks.set(task.name, task);
  }

  public startScheduler(): void {
    logger.info('Starting task scheduler...');

    for (const [name, task] of this.tasks) {
      if (task.enabled) {
        cron.schedule(task.schedule, async () => {
          if (task.isRunning) {
            logger.warn(`Task ${name} is already running, skipping...`);
            return;
          }

          try {
            task.isRunning = true;
            task.lastRun = new Date();
            
            logger.info(`Starting task: ${name}`);
            await task.task();
            logger.info(`Completed task: ${name}`);

          } catch (error) {
            logger.error(`Error in task ${name}:`, error);
          } finally {
            task.isRunning = false;
          }
        });

        logger.info(`Scheduled task: ${name} (${task.schedule})`);
      }
    }

    logger.info('Task scheduler started successfully');
  }

  public stopScheduler(): void {
    logger.info('Stopping task scheduler...');
    // Note: node-cron doesn't provide a direct way to stop all tasks
    // In production, you might want to keep references to the scheduled tasks
    logger.info('Task scheduler stopped');
  }

  public getTaskStatus(): Array<{
    name: string;
    schedule: string;
    enabled: boolean;
    isRunning: boolean;
    lastRun?: Date;
  }> {
    return Array.from(this.tasks.values()).map(task => ({
      name: task.name,
      schedule: task.schedule,
      enabled: task.enabled,
      isRunning: task.isRunning,
      lastRun: task.lastRun,
    }));
  }

  public enableTask(taskName: string): void {
    const task = this.tasks.get(taskName);
    if (task) {
      task.enabled = true;
      logger.info(`Task ${taskName} enabled`);
    }
  }

  public disableTask(taskName: string): void {
    const task = this.tasks.get(taskName);
    if (task) {
      task.enabled = false;
      logger.info(`Task ${taskName} disabled`);
    }
  }

  // Task implementations
  private async updatePrices(): Promise<void> {
    try {
      const result = await this.priceService.updateAllPrices();
      logger.info(`Price update completed: ${result.updated} updated, ${result.failed} failed`);
    } catch (error) {
      logger.error('Price update task failed:', error);
      throw error;
    }
  }

  private async runTechnicalAnalysis(): Promise<void> {
    try {
      const result = await this.technicalAnalysisService.analyzeAllSymbols();
      logger.info(`Technical analysis completed: ${result.analyzed} analyzed, ${result.signals} signals`);
    } catch (error) {
      logger.error('Technical analysis task failed:', error);
      throw error;
    }
  }

  private async runChainMonitoring(): Promise<void> {
    try {
      const result = await this.chainMonitoringService.runFullMonitoring();
      logger.info(`Chain monitoring completed: ${result.largeTransactions.length} large transactions, ${result.alerts.length} alerts`);
    } catch (error) {
      logger.error('Chain monitoring task failed:', error);
      throw error;
    }
  }

  private async sendHourlyNotifications(): Promise<void> {
    try {
      const result = await this.notificationService.sendScheduledNotifications('HOURLY');
      logger.info(`Hourly notifications sent: ${result.sent} sent, ${result.failed} failed`);
    } catch (error) {
      logger.error('Hourly notifications task failed:', error);
      throw error;
    }
  }

  private async sendDailyNotifications(): Promise<void> {
    try {
      const result = await this.notificationService.sendScheduledNotifications('DAILY');
      logger.info(`Daily notifications sent: ${result.sent} sent, ${result.failed} failed`);
    } catch (error) {
      logger.error('Daily notifications task failed:', error);
      throw error;
    }
  }

  private async sendWeeklyNotifications(): Promise<void> {
    try {
      const result = await this.notificationService.sendScheduledNotifications('WEEKLY');
      logger.info(`Weekly notifications sent: ${result.sent} sent, ${result.failed} failed`);
    } catch (error) {
      logger.error('Weekly notifications task failed:', error);
      throw error;
    }
  }

  private async cleanupOldData(): Promise<void> {
    try {
      // Clean up old price data (keep 90 days)
      // Clean up old notifications (keep 30 days)
      // Clean up old technical signals (keep 30 days)
      
      logger.info('Data cleanup completed');
    } catch (error) {
      logger.error('Data cleanup task failed:', error);
      throw error;
    }
  }

  private async systemHealthCheck(): Promise<void> {
    try {
      // Check database connection
      // Check API endpoints
      // Check memory usage
      // Check disk space
      
      logger.info('System health check completed');
    } catch (error) {
      logger.error('System health check failed:', error);
      throw error;
    }
  }

  // Manual task execution
  public async runTask(taskName: string): Promise<void> {
    const task = this.tasks.get(taskName);
    if (!task) {
      throw new Error(`Task ${taskName} not found`);
    }

    if (task.isRunning) {
      throw new Error(`Task ${taskName} is already running`);
    }

    try {
      task.isRunning = true;
      task.lastRun = new Date();
      
      logger.info(`Manually running task: ${taskName}`);
      await task.task();
      logger.info(`Manually completed task: ${taskName}`);

    } catch (error) {
      logger.error(`Error in manual task ${taskName}:`, error);
      throw error;
    } finally {
      task.isRunning = false;
    }
  }
}
