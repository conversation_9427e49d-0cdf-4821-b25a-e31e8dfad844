import config, { validateConfig } from './config';
import logger from './config/logger';
import { connectDatabase } from './config/database';
import TelegramBot from './bot';
import { TaskScheduler } from './schedulers/TaskScheduler';
import { DataInitializationService } from './services/DataInitializationService';
import { MonitoringService } from './services/MonitoringService';
import { <PERSON>rrorHandler } from './utils/ErrorHandler';

async function main(): Promise<void> {
  try {
    // Validate configuration
    logger.info('🔧 Validating configuration...');
    validateConfig();
    logger.info('✅ Configuration validated successfully');

    // Connect to database
    logger.info('🗄️ Connecting to database...');
    await connectDatabase();

    // Initialize data if needed
    logger.info('📊 Initializing cryptocurrency data...');
    const dataInitService = new DataInitializationService();
    const initResult = await dataInitService.initializeCryptocurrencies();
    logger.info(`Data initialization: ${initResult.created} created, ${initResult.skipped} skipped`);

    // Start monitoring service
    logger.info('📈 Starting monitoring service...');
    const monitoringService = new MonitoringService();

    // Perform initial health check
    const healthCheck = await monitoringService.performHealthCheck();
    logger.info(`Initial health check: ${healthCheck.overall}`);

    // Start Telegram bot
    logger.info('🤖 Starting Telegram bot...');
    const bot = new TelegramBot();
    await bot.start();

    // Start task scheduler
    logger.info('⏰ Starting task scheduler...');
    const scheduler = new TaskScheduler();
    scheduler.startScheduler();

    logger.info('🚀 Application started successfully');
    logger.info(`Environment: ${config.app.nodeEnv}`);
    logger.info(`Port: ${config.app.port}`);
    logger.info(`Log Level: ${config.app.logLevel}`);

    // Send startup notification
    await monitoringService.sendCustomAlert(
      'System Startup',
      `Crypto Bot started successfully in ${config.app.nodeEnv} mode`,
      'info'
    );

  } catch (error) {
    logger.error('❌ Failed to start application:', error);
    ErrorHandler.handleError(error as Error);
    process.exit(1);
  }
}

// Handle unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  logger.error('Unhandled Rejection at:', promise, 'reason:', reason);
  process.exit(1);
});

// Handle uncaught exceptions
process.on('uncaughtException', (error) => {
  logger.error('Uncaught Exception:', error);
  process.exit(1);
});

// Start the application
main().catch((error) => {
  logger.error('Application startup failed:', error);
  process.exit(1);
});
