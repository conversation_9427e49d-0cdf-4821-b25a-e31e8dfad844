import logger from '../config/logger';
import { AppError } from '../types';

export class CustomError extends Error implements AppError {
  public statusCode: number;
  public isOperational: boolean;

  constructor(message: string, statusCode: number = 500, isOperational: boolean = true) {
    super(message);
    this.statusCode = statusCode;
    this.isOperational = isOperational;

    Error.captureStackTrace(this, this.constructor);
  }
}

export class ValidationError extends CustomError {
  constructor(message: string) {
    super(message, 400, true);
    this.name = 'ValidationError';
  }
}

export class NotFoundError extends CustomError {
  constructor(message: string = 'Resource not found') {
    super(message, 404, true);
    this.name = 'NotFoundError';
  }
}

export class UnauthorizedError extends CustomError {
  constructor(message: string = 'Unauthorized access') {
    super(message, 401, true);
    this.name = 'UnauthorizedError';
  }
}

export class ForbiddenError extends CustomError {
  constructor(message: string = 'Forbidden access') {
    super(message, 403, true);
    this.name = 'ForbiddenError';
  }
}

export class RateLimitError extends CustomError {
  constructor(message: string = 'Rate limit exceeded') {
    super(message, 429, true);
    this.name = 'RateLimitError';
  }
}

export class ExternalAPIError extends CustomError {
  public apiName: string;
  public originalError?: Error;

  constructor(apiName: string, message: string, originalError?: Error) {
    super(`${apiName} API Error: ${message}`, 502, true);
    this.name = 'ExternalAPIError';
    this.apiName = apiName;
    this.originalError = originalError;
  }
}

export class DatabaseError extends CustomError {
  public operation: string;
  public originalError?: Error;

  constructor(operation: string, message: string, originalError?: Error) {
    super(`Database Error (${operation}): ${message}`, 500, true);
    this.name = 'DatabaseError';
    this.operation = operation;
    this.originalError = originalError;
  }
}

export class ErrorHandler {
  public static handleError(error: Error): void {
    if (error instanceof CustomError) {
      this.handleCustomError(error);
    } else {
      this.handleGenericError(error);
    }
  }

  private static handleCustomError(error: CustomError): void {
    const logLevel = error.statusCode >= 500 ? 'error' : 'warn';
    
    logger[logLevel]('Custom Error:', {
      name: error.name,
      message: error.message,
      statusCode: error.statusCode,
      isOperational: error.isOperational,
      stack: error.stack,
    });

    // Send to monitoring service if critical
    if (error.statusCode >= 500) {
      this.sendToMonitoring(error);
    }
  }

  private static handleGenericError(error: Error): void {
    logger.error('Unhandled Error:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
    });

    // All unhandled errors are considered critical
    this.sendToMonitoring(error);
  }

  private static sendToMonitoring(error: Error): void {
    // In production, you would send this to a monitoring service
    // like Sentry, DataDog, or custom webhook
    logger.error('CRITICAL ERROR - Sending to monitoring:', {
      error: error.message,
      stack: error.stack,
      timestamp: new Date().toISOString(),
    });
  }

  public static wrapAsync<T extends any[], R>(
    fn: (...args: T) => Promise<R>
  ): (...args: T) => Promise<R> {
    return async (...args: T): Promise<R> => {
      try {
        return await fn(...args);
      } catch (error) {
        this.handleError(error as Error);
        throw error;
      }
    };
  }

  public static createErrorResponse(error: Error): {
    success: false;
    error: string;
    statusCode: number;
    timestamp: string;
  } {
    let statusCode = 500;
    let message = 'Internal server error';

    if (error instanceof CustomError) {
      statusCode = error.statusCode;
      message = error.message;
    }

    return {
      success: false,
      error: message,
      statusCode,
      timestamp: new Date().toISOString(),
    };
  }

  public static isOperationalError(error: Error): boolean {
    if (error instanceof CustomError) {
      return error.isOperational;
    }
    return false;
  }
}

// Utility functions for common error scenarios
export const handleDatabaseOperation = async <T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    throw new DatabaseError(operation, (error as Error).message, error as Error);
  }
};

export const handleExternalAPI = async <T>(
  apiName: string,
  fn: () => Promise<T>
): Promise<T> => {
  try {
    return await fn();
  } catch (error) {
    throw new ExternalAPIError(apiName, (error as Error).message, error as Error);
  }
};

export const validateRequired = (value: any, fieldName: string): void => {
  if (value === undefined || value === null || value === '') {
    throw new ValidationError(`${fieldName} is required`);
  }
};

export const validateEmail = (email: string): void => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    throw new ValidationError('Invalid email format');
  }
};

export const validatePositiveNumber = (value: number, fieldName: string): void => {
  if (typeof value !== 'number' || value <= 0) {
    throw new ValidationError(`${fieldName} must be a positive number`);
  }
};

export const validateStringLength = (
  value: string,
  fieldName: string,
  minLength: number = 1,
  maxLength: number = 255
): void => {
  if (typeof value !== 'string') {
    throw new ValidationError(`${fieldName} must be a string`);
  }
  
  if (value.length < minLength) {
    throw new ValidationError(`${fieldName} must be at least ${minLength} characters long`);
  }
  
  if (value.length > maxLength) {
    throw new ValidationError(`${fieldName} must be no more than ${maxLength} characters long`);
  }
};

export const validateEnum = <T>(
  value: T,
  enumObject: Record<string, T>,
  fieldName: string
): void => {
  const validValues = Object.values(enumObject);
  if (!validValues.includes(value)) {
    throw new ValidationError(
      `${fieldName} must be one of: ${validValues.join(', ')}`
    );
  }
};

// Rate limiting helper
export class RateLimiter {
  private requests: Map<string, number[]> = new Map();

  public checkRateLimit(
    identifier: string,
    maxRequests: number,
    windowMs: number
  ): void {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!this.requests.has(identifier)) {
      this.requests.set(identifier, []);
    }
    
    const userRequests = this.requests.get(identifier)!;
    
    // Remove old requests outside the window
    const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
    
    if (validRequests.length >= maxRequests) {
      throw new RateLimitError(`Rate limit exceeded. Max ${maxRequests} requests per ${windowMs}ms`);
    }
    
    // Add current request
    validRequests.push(now);
    this.requests.set(identifier, validRequests);
  }

  public getRemainingRequests(
    identifier: string,
    maxRequests: number,
    windowMs: number
  ): number {
    const now = Date.now();
    const windowStart = now - windowMs;
    
    if (!this.requests.has(identifier)) {
      return maxRequests;
    }
    
    const userRequests = this.requests.get(identifier)!;
    const validRequests = userRequests.filter(timestamp => timestamp > windowStart);
    
    return Math.max(0, maxRequests - validRequests.length);
  }
}

// Circuit breaker pattern for external services
export class CircuitBreaker {
  private failures: number = 0;
  private lastFailureTime: number = 0;
  private state: 'CLOSED' | 'OPEN' | 'HALF_OPEN' = 'CLOSED';

  constructor(
    private failureThreshold: number = 5,
    private recoveryTimeout: number = 60000 // 1 minute
  ) {}

  public async execute<T>(fn: () => Promise<T>): Promise<T> {
    if (this.state === 'OPEN') {
      if (Date.now() - this.lastFailureTime > this.recoveryTimeout) {
        this.state = 'HALF_OPEN';
      } else {
        throw new CustomError('Circuit breaker is OPEN', 503);
      }
    }

    try {
      const result = await fn();
      this.onSuccess();
      return result;
    } catch (error) {
      this.onFailure();
      throw error;
    }
  }

  private onSuccess(): void {
    this.failures = 0;
    this.state = 'CLOSED';
  }

  private onFailure(): void {
    this.failures++;
    this.lastFailureTime = Date.now();
    
    if (this.failures >= this.failureThreshold) {
      this.state = 'OPEN';
    }
  }

  public getState(): string {
    return this.state;
  }

  public getFailures(): number {
    return this.failures;
  }
}
