// User related types
export interface User {
  id: string;
  telegramId: number;
  username?: string;
  firstName?: string;
  lastName?: string;
  isActive: boolean;
  isPremium: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserSubscription {
  id: string;
  userId: string;
  coinId: string;
  isActive: boolean;
  notificationSettings: NotificationSettings;
  createdAt: Date;
  updatedAt: Date;
}

export interface NotificationSettings {
  enablePriceAlerts: boolean;
  enableTechnicalSignals: boolean;
  enableChainAlerts: boolean;
  pushFrequency: PushFrequency;
  priceChangeThreshold: number; // percentage
  volumeChangeThreshold: number; // percentage
}

export enum PushFrequency {
  HOURLY = 'hourly',
  EVERY_4_HOURS = 'every_4_hours',
  DAILY = 'daily',
  WEEKLY = 'weekly',
}

// Cryptocurrency related types
export interface Cryptocurrency {
  id: string;
  symbol: string;
  name: string;
  coinGeckoId: string;
  contractAddress?: string;
  blockchain: Blockchain;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum Blockchain {
  ETHEREUM = 'ethereum',
  BSC = 'bsc',
  POLYGON = 'polygon',
  ARBITRUM = 'arbitrum',
  OPTIMISM = 'optimism',
}

// Price and market data types
export interface PriceData {
  coinId: string;
  price: number;
  priceChange24h: number;
  priceChangePercentage24h: number;
  marketCap: number;
  volume24h: number;
  timestamp: Date;
}

export interface HistoricalPrice {
  id: string;
  coinId: string;
  price: number;
  volume: number;
  marketCap: number;
  timestamp: Date;
}

// Technical analysis types
export interface TechnicalIndicators {
  coinId: string;
  macd: MACDData;
  rsi: RSIData;
  timestamp: Date;
}

export interface MACDData {
  macd: number;
  signal: number;
  histogram: number;
  isPulseSignal: boolean; // MACD line crosses signal line and histogram turns positive
}

export interface RSIData {
  rsi: number;
  isOversold: boolean; // RSI < 30
  isOverbought: boolean; // RSI > 70
  isInRange: boolean; // RSI between 30-70
}

export interface TechnicalSignal {
  id: string;
  coinId: string;
  signalType: SignalType;
  strength: SignalStrength;
  description: string;
  indicators: TechnicalIndicators;
  createdAt: Date;
}

export enum SignalType {
  BUY = 'buy',
  SELL = 'sell',
  HOLD = 'hold',
  STRONG_BUY = 'strong_buy',
  STRONG_SELL = 'strong_sell',
}

export enum SignalStrength {
  WEAK = 'weak',
  MODERATE = 'moderate',
  STRONG = 'strong',
  VERY_STRONG = 'very_strong',
}

// Chain analysis types
export interface ChainTransaction {
  id: string;
  hash: string;
  from: string;
  to: string;
  value: string; // in wei for ETH, smallest unit for other chains
  valueUSD: number;
  blockchain: Blockchain;
  blockNumber: number;
  timestamp: Date;
  isLargeTransaction: boolean;
}

export interface WhaleAddress {
  id: string;
  address: string;
  blockchain: Blockchain;
  balance: string;
  balanceUSD: number;
  label?: string;
  isExchange: boolean;
  lastActivity: Date;
}

export interface ChainAlert {
  id: string;
  alertType: ChainAlertType;
  blockchain: Blockchain;
  description: string;
  data: Record<string, any>;
  severity: AlertSeverity;
  createdAt: Date;
}

export enum ChainAlertType {
  LARGE_TRANSACTION = 'large_transaction',
  WHALE_MOVEMENT = 'whale_movement',
  EXCHANGE_INFLOW = 'exchange_inflow',
  EXCHANGE_OUTFLOW = 'exchange_outflow',
  UNUSUAL_VOLUME = 'unusual_volume',
}

export enum AlertSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// API response types
export interface CoinGeckoPrice {
  id: string;
  symbol: string;
  name: string;
  current_price: number;
  market_cap: number;
  market_cap_rank: number;
  fully_diluted_valuation: number;
  total_volume: number;
  high_24h: number;
  low_24h: number;
  price_change_24h: number;
  price_change_percentage_24h: number;
  market_cap_change_24h: number;
  market_cap_change_percentage_24h: number;
  circulating_supply: number;
  total_supply: number;
  max_supply: number;
  ath: number;
  ath_change_percentage: number;
  ath_date: string;
  atl: number;
  atl_change_percentage: number;
  atl_date: string;
  last_updated: string;
}

export interface EtherscanTransaction {
  blockNumber: string;
  timeStamp: string;
  hash: string;
  from: string;
  to: string;
  value: string;
  gas: string;
  gasPrice: string;
  gasUsed: string;
  isError: string;
}

// Bot command types
export interface BotCommand {
  command: string;
  description: string;
  handler: (ctx: any) => Promise<void>;
}

export interface BotContext {
  userId: string;
  chatId: number;
  messageId: number;
  user: User;
}

// Notification types
export interface NotificationMessage {
  userId: string;
  type: NotificationType;
  title: string;
  message: string;
  data?: Record<string, any>;
  priority: NotificationPriority;
  scheduledAt?: Date;
}

export enum NotificationType {
  PRICE_ALERT = 'price_alert',
  TECHNICAL_SIGNAL = 'technical_signal',
  CHAIN_ALERT = 'chain_alert',
  SYSTEM_NOTIFICATION = 'system_notification',
}

export enum NotificationPriority {
  LOW = 'low',
  NORMAL = 'normal',
  HIGH = 'high',
  URGENT = 'urgent',
}

// Error types
export interface AppError extends Error {
  statusCode: number;
  isOperational: boolean;
}

// Utility types
export type Pagination = {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
};

export type SortOrder = 'asc' | 'desc';

export type TimeFrame = '1h' | '4h' | '1d' | '1w' | '1m';

export type ApiResponse<T> = {
  success: boolean;
  data?: T;
  error?: string;
  pagination?: Pagination;
};
