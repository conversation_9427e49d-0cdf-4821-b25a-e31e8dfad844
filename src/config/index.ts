import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

interface Config {
  // Telegram Bot Configuration
  telegram: {
    botToken: string;
  };

  // Database Configuration
  database: {
    url: string;
  };

  // API Keys
  apiKeys: {
    coinGecko?: string;
    etherscan: string;
    bscScan: string;
  };

  // Redis Configuration
  redis: {
    url: string;
  };

  // Application Configuration
  app: {
    nodeEnv: string;
    port: number;
    logLevel: string;
  };

  // Security
  security: {
    jwtSecret: string;
    encryptionKey: string;
  };

  // Rate Limiting
  rateLimit: {
    windowMs: number;
    maxRequests: number;
  };

  // Monitoring
  monitoring: {
    webhookUrl?: string;
  };

  // Feature Flags
  features: {
    enableChainMonitoring: boolean;
    enableTechnicalAnalysis: boolean;
    enablePriceAlerts: boolean;
  };
}

const config: Config = {
  telegram: {
    botToken: process.env.TELEGRAM_BOT_TOKEN || '',
  },

  database: {
    url: process.env.DATABASE_URL || '',
  },

  apiKeys: {
    coinGecko: process.env.COINGECKO_API_KEY,
    etherscan: process.env.ETHERSCAN_API_KEY || '',
    bscScan: process.env.BSCSCAN_API_KEY || '',
  },

  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
  },

  app: {
    nodeEnv: process.env.NODE_ENV || 'development',
    port: parseInt(process.env.PORT || '3000', 10),
    logLevel: process.env.LOG_LEVEL || 'info',
  },

  security: {
    jwtSecret: process.env.JWT_SECRET || '',
    encryptionKey: process.env.ENCRYPTION_KEY || '',
  },

  rateLimit: {
    windowMs: parseInt(process.env.API_RATE_LIMIT_WINDOW_MS || '60000', 10),
    maxRequests: parseInt(process.env.API_RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },

  monitoring: {
    webhookUrl: process.env.WEBHOOK_URL,
  },

  features: {
    enableChainMonitoring: process.env.ENABLE_CHAIN_MONITORING === 'true',
    enableTechnicalAnalysis: process.env.ENABLE_TECHNICAL_ANALYSIS === 'true',
    enablePriceAlerts: process.env.ENABLE_PRICE_ALERTS === 'true',
  },
};

// Validation function
export function validateConfig(): void {
  const requiredFields = [
    'TELEGRAM_BOT_TOKEN',
    'DATABASE_URL',
    'ETHERSCAN_API_KEY',
    'BSCSCAN_API_KEY',
    'JWT_SECRET',
    'ENCRYPTION_KEY',
  ];

  const missingFields = requiredFields.filter(field => !process.env[field]);

  if (missingFields.length > 0) {
    throw new Error(`Missing required environment variables: ${missingFields.join(', ')}`);
  }
}

export default config;
