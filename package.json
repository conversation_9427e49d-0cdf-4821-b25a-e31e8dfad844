{"name": "tg-bot-demo", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "test": "echo \"Error: no test specified\" && exit 1", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio"}, "repository": {"type": "git", "url": "git+https://github.com/Rabbit937/tg-bot-demo.git"}, "keywords": [], "author": "", "license": "ISC", "type": "commonjs", "bugs": {"url": "https://github.com/Rabbit937/tg-bot-demo/issues"}, "homepage": "https://github.com/Rabbit937/tg-bot-demo#readme", "dependencies": {"@prisma/client": "^6.10.1", "@types/node": "^24.0.7", "axios": "^1.10.0", "dotenv": "^17.0.0", "node-cron": "^4.1.1", "nodemon": "^3.1.10", "prisma": "^6.10.1", "technicalindicators": "^3.1.0", "telegraf": "^4.16.3", "ts-node": "^10.9.2", "typescript": "^5.8.3", "winston": "^3.17.0"}, "devDependencies": {"@types/node-cron": "^3.0.11", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "eslint": "^9.30.0", "eslint-config-prettier": "^10.1.5", "eslint-plugin-prettier": "^5.5.1", "prettier": "^3.6.2"}}