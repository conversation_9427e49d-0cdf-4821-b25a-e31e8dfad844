# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/crypto_bot_db?schema=public"

# API Keys
COINGECKO_API_KEY=your_coingecko_api_key_here
ETHERSCAN_API_KEY=your_etherscan_api_key_here
BSCSCAN_API_KEY=your_bscscan_api_key_here

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379

# Application Configuration
NODE_ENV=development
PORT=3000
LOG_LEVEL=info

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Rate Limiting
API_RATE_LIMIT_WINDOW_MS=60000
API_RATE_LIMIT_MAX_REQUESTS=100

# Monitoring Configuration
WEBHOOK_URL=your_webhook_url_for_alerts

# Feature Flags
ENABLE_CHAIN_MONITORING=true
ENABLE_TECHNICAL_ANALYSIS=true
ENABLE_PRICE_ALERTS=true
