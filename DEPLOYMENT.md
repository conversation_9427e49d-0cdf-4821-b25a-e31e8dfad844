# 加密货币交易辅助Telegram机器人 - 部署指南

## 目录
- [系统要求](#系统要求)
- [环境配置](#环境配置)
- [本地开发部署](#本地开发部署)
- [生产环境部署](#生产环境部署)
- [数据库迁移](#数据库迁移)
- [监控和维护](#监控和维护)
- [故障排除](#故障排除)

## 系统要求

### 最低配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB SSD
- **网络**: 稳定的互联网连接

### 推荐配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 50GB SSD
- **网络**: 高速互联网连接

### 软件要求
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (如果不使用Docker)
- PostgreSQL 15+ (如果不使用Docker)
- Redis 7+ (如果不使用Docker)

## 环境配置

### 1. 获取API密钥

#### Telegram Bot Token
1. 联系 [@BotFather](https://t.me/botfather) 创建新机器人
2. 获取Bot Token
3. 设置机器人命令：
   ```
   start - 开始使用机器人
   help - 查看帮助信息
   subscribe - 订阅币种监控
   unsubscribe - 取消订阅
   list - 查看订阅列表
   price - 查询价格
   settings - 个人设置
   status - 机器人状态
   ```

#### CoinGecko API Key (可选)
1. 访问 [CoinGecko API](https://www.coingecko.com/en/api)
2. 注册账户并获取API密钥
3. 免费版有请求限制，建议使用付费版

#### Etherscan API Key
1. 访问 [Etherscan](https://etherscan.io/apis)
2. 注册账户并创建API密钥

#### BSCScan API Key
1. 访问 [BSCScan](https://bscscan.com/apis)
2. 注册账户并创建API密钥

### 2. 环境变量配置

复制环境变量模板：
```bash
cp .env.example .env
```

编辑 `.env` 文件：
```bash
# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here

# Database Configuration
DATABASE_URL="postgresql://postgres:your_password@localhost:5432/crypto_bot_db?schema=public"

# API Keys
COINGECKO_API_KEY=your_coingecko_api_key_here
ETHERSCAN_API_KEY=your_etherscan_api_key_here
BSCSCAN_API_KEY=your_bscscan_api_key_here

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Application Configuration
NODE_ENV=production
PORT=3000
LOG_LEVEL=info

# Security
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Docker specific
POSTGRES_PASSWORD=your_postgres_password
REDIS_PASSWORD=your_redis_password
GRAFANA_PASSWORD=your_grafana_password
```

## 本地开发部署

### 1. 克隆项目
```bash
git clone https://github.com/your-username/tg-bot-demo.git
cd tg-bot-demo
```

### 2. 安装依赖
```bash
npm install
```

### 3. 启动开发环境
```bash
# 使用Docker Compose
docker-compose -f docker-compose.dev.yml up -d

# 或者本地运行
npm run dev
```

### 4. 数据库初始化
```bash
# 生成Prisma客户端
npm run db:generate

# 运行数据库迁移
npm run db:migrate

# 初始化数据
npm run init-data
```

### 5. 访问开发工具
- **应用**: http://localhost:3000
- **数据库管理**: http://localhost:8080 (Adminer)
- **Redis管理**: http://localhost:8081 (Redis Commander)

## 生产环境部署

### 1. 服务器准备
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 创建应用目录
sudo mkdir -p /opt/crypto-bot
sudo chown $USER:$USER /opt/crypto-bot
```

### 2. 部署应用
```bash
# 克隆项目到服务器
cd /opt/crypto-bot
git clone https://github.com/your-username/tg-bot-demo.git .

# 配置环境变量
cp .env.example .env
nano .env  # 编辑配置

# 启动生产环境
docker-compose up -d
```

### 3. SSL证书配置 (可选)
```bash
# 使用Let's Encrypt
sudo apt install certbot
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ./ssl/
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ./ssl/
```

### 4. 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 22    # SSH
sudo ufw allow 80    # HTTP
sudo ufw allow 443   # HTTPS
sudo ufw enable
```

## 数据库迁移

### 初始化数据库
```bash
# 进入应用容器
docker-compose exec app sh

# 运行迁移
npx prisma migrate deploy

# 生成客户端
npx prisma generate

# 初始化加密货币数据
npm run init-crypto-data
```

### 备份数据库
```bash
# 创建备份
docker-compose exec postgres pg_dump -U postgres crypto_bot_db > backup_$(date +%Y%m%d_%H%M%S).sql

# 恢复备份
docker-compose exec -T postgres psql -U postgres crypto_bot_db < backup_file.sql
```

## 监控和维护

### 1. 日志查看
```bash
# 查看应用日志
docker-compose logs -f app

# 查看特定服务日志
docker-compose logs -f postgres
docker-compose logs -f redis
```

### 2. 健康检查
```bash
# 检查服务状态
docker-compose ps

# 健康检查端点
curl http://localhost:3000/health
```

### 3. 性能监控
- **Grafana**: http://localhost:3001
- **Prometheus**: http://localhost:9090

### 4. 定期维护
```bash
# 清理Docker资源
docker system prune -f

# 更新应用
git pull
docker-compose build --no-cache
docker-compose up -d
```

## 故障排除

### 常见问题

#### 1. 容器启动失败
```bash
# 检查日志
docker-compose logs app

# 重新构建
docker-compose build --no-cache app
docker-compose up -d
```

#### 2. 数据库连接失败
```bash
# 检查数据库状态
docker-compose exec postgres pg_isready -U postgres

# 重启数据库
docker-compose restart postgres
```

#### 3. API请求失败
- 检查API密钥是否正确
- 验证网络连接
- 查看API使用限制

#### 4. 内存不足
```bash
# 检查内存使用
docker stats

# 增加swap空间
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 紧急恢复

#### 完全重新部署
```bash
# 停止所有服务
docker-compose down

# 清理数据 (谨慎操作!)
docker-compose down -v

# 重新部署
docker-compose up -d
```

#### 数据恢复
```bash
# 从备份恢复数据库
docker-compose exec -T postgres psql -U postgres crypto_bot_db < latest_backup.sql

# 重启应用
docker-compose restart app
```

## 安全建议

1. **定期更新**: 保持系统和依赖包最新
2. **备份策略**: 每日自动备份数据库
3. **监控告警**: 设置系统监控和告警
4. **访问控制**: 限制服务器访问权限
5. **密钥管理**: 定期轮换API密钥和密码

## 支持

如遇到问题，请：
1. 查看日志文件
2. 检查配置文件
3. 参考故障排除部分
4. 提交Issue到GitHub仓库

---

**注意**: 本指南假设您具备基本的Linux系统管理和Docker使用经验。在生产环境部署前，请确保充分测试所有功能。
